#!/usr/bin/env python3
"""
Test script for local_api_server endpoints
"""

import requests
import json
import time
from loguru import logger

# Server configuration
BASE_URL = "http://localhost:8002"

def test_start_category_influencer():
    """Test the start-category-influencer-data endpoint"""
    logger.info("Testing start-category-influencer-data endpoint...")

    url = f"{BASE_URL}/api/start-category-influencer-data"

    # Test payload
    payload = {
        "category": "food",
        "count": 3,
        "location": "Nigeria"
    }

    try:
        response = requests.post(url, json=payload)
        logger.info(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.success(f"✅ Successfully started task")
            logger.info(f"Task ID: {data.get('task_id')}")
            logger.info(f"Status: {data.get('status')}")
            logger.info(f"Message: {data.get('message')}")
            return data.get('task_id')
        else:
            logger.error(f"❌ Failed with status {response.status_code}")
            logger.error(f"Response: {response.text}")
            return None

    except Exception as e:
        logger.error(f"❌ Exception: {e}")
        return None

def test_check_task_status(task_id):
    """Test the check-task-status endpoint"""
    if not task_id:
        logger.warning("No task ID provided, skipping check-task-status test")
        return

    logger.info(f"Testing check-task-status endpoint for task {task_id}...")

    url = f"{BASE_URL}/api/check-task-status/{task_id}"

    try:
        response = requests.get(url)
        logger.info(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.success(f"✅ Successfully checked task status")
            logger.info(f"Task ID: {data.get('task_id')}")
            logger.info(f"Status: {data.get('status')}")
            logger.info(f"Progress: {data.get('progress', 'N/A')}")
            logger.info(f"Message: {data.get('message', 'N/A')}")
            return data.get('status')
        else:
            logger.error(f"❌ Failed with status {response.status_code}")
            logger.error(f"Response: {response.text}")
            return None

    except Exception as e:
        logger.error(f"❌ Exception: {e}")
        return None

def test_get_task_result(task_id):
    """Test the get-task-result endpoint"""
    if not task_id:
        logger.warning("No task ID provided, skipping get-task-result test")
        return

    logger.info(f"Testing get-task-result endpoint for task {task_id}...")

    url = f"{BASE_URL}/api/get-task-result/{task_id}"

    try:
        response = requests.get(url)
        logger.info(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.success(f"✅ Successfully retrieved task result")
            logger.info(f"Task ID: {data.get('task_id')}")
            logger.info(f"Status: {data.get('status')}")

            # Check if we have results
            if 'result' in data and data['result']:
                result = data['result']
                logger.info(f"Number of influencers: {len(result)}")

                # Show details for first influencer
                if len(result) > 0:
                    first_influencer = result[0]
                    logger.info(f"First influencer: {first_influencer.get('name', 'Unknown')}")

                    # Check social media data
                    for platform in ['youtube', 'instagram', 'facebook', 'tiktok']:
                        if platform in first_influencer:
                            platform_data = first_influencer[platform]
                            followers = platform_data.get('followers_count', '0')
                            logger.info(f"  {platform.title()}: {followers} followers")

                # Show total followers calculation
                if 'total_followers' in result[0]:
                    logger.info(f"Total followers: {result[0]['total_followers']}")

            else:
                logger.warning("No results found in response")

            return data
        else:
            logger.error(f"❌ Failed with status {response.status_code}")
            logger.error(f"Response: {response.text}")
            return None

    except Exception as e:
        logger.error(f"❌ Exception: {e}")
        return None

def main():
    """Main test function"""
    logger.info("Starting endpoint tests...")
    logger.info("="*60)

    # Test 1: Start a new task
    logger.info("TEST 1: Starting new influencer search task")
    task_id = test_start_category_influencer()

    if not task_id:
        logger.error("Failed to start task, stopping tests")
        return

    logger.info("="*60)

    # Test 2: Check task status (wait a bit for processing)
    logger.info("TEST 2: Checking task status")
    logger.info("Waiting 10 seconds for task to start processing...")
    time.sleep(10)

    status = test_check_task_status(task_id)

    logger.info("="*60)

    # Test 3: Wait for completion and get results
    logger.info("TEST 3: Waiting for task completion and getting results")

    max_wait_time = 300  # 5 minutes
    check_interval = 15  # Check every 15 seconds
    elapsed_time = 0

    while elapsed_time < max_wait_time:
        status = test_check_task_status(task_id)

        if status == "completed":
            logger.success("Task completed! Getting results...")
            break
        elif status == "failed":
            logger.error("Task failed!")
            return
        else:
            logger.info(f"Task still running... (elapsed: {elapsed_time}s)")
            time.sleep(check_interval)
            elapsed_time += check_interval

    if elapsed_time >= max_wait_time:
        logger.warning("Task taking too long, getting current results anyway...")

    # Get the final results
    result = test_get_task_result(task_id)

    logger.info("="*60)
    logger.info("Endpoint tests completed!")

if __name__ == "__main__":
    main()
