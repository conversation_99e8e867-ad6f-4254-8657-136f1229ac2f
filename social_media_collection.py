import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from names_collection import perplexity_data
import httpx
import json
import time
import asyncio
import aiohttp
import random
import re
from loguru import logger
from functools import wraps
from asyncio import Semaphore
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Union
import requests

# Import the scrapers
from perplexity_scraper import get_instagram_details, fetch_youtube_feed, get_facebook_details, get_facebook_details_batch, get_tiktok_details, get_contact_details

load_dotenv()

# Define API keys
API_KEY = os.getenv("PERPLEXITY_AI_API")
try:
    from tavily import TavilyClient
    tavily_client = TavilyClient(api_key=os.getenv("TAVILY_API_KEY")) if os.getenv("TAVILY_API_KEY") else None
except ImportError:
    tavily_client = None

# Check for missing environment variables
if not os.getenv("PERPLEXITY_AI_API"):
    logger.error("PERPLEXITY_AI_API key is missing from the environment variables.")
    raise EnvironmentError("Missing API keys.")

llm = ChatOpenAI(model="gpt-4o")

# Define Pydantic models for structured outputs
class SocialMediaProfile(BaseModel):
    social_media: str
    followers: Optional[int] = None
    following: Optional[int] = None
    name: Optional[str] = None
    username: Optional[str] = None
    social_media_url: Optional[str] = None
    average_engagements: Optional[int] = None
    average_views: Optional[int] = None
    potential_reach: Optional[int] = None
    potential_impressions: Optional[int] = None

class ContactInfo(BaseModel):
    email: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    address: Optional[str] = None
    management_company: Optional[str] = None
    manager_name: Optional[str] = None
    manager_email: Optional[str] = None
    manager_phone: Optional[str] = None

class BasicInfluencerInfo(BaseModel):
    name: str
    category: str
    description: Optional[str] = None
    url: Optional[str] = None

class InfluencerNamesList(BaseModel):
    names: List[str]

# Add a global rate limiter for all API calls
class GlobalRateLimiter:
    def __init__(self, calls_per_minute=50):
        self.calls_per_minute = calls_per_minute
        self.call_timestamps = []
        self.lock = asyncio.Lock()

    async def wait(self):
        async with self.lock:
            now = time.time()
            # Remove timestamps older than 1 minute
            self.call_timestamps = [ts for ts in self.call_timestamps if now - ts < 60]

            # If we've reached the limit, wait until we can make another call
            if len(self.call_timestamps) >= self.calls_per_minute:
                wait_time = 60 - (now - self.call_timestamps[0]) + random.uniform(0.1, 1.0)
                logger.info(f"Global rate limit reached, waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
                # After waiting, remove timestamps older than 1 minute again
                now = time.time()
                self.call_timestamps = [ts for ts in self.call_timestamps if now - ts < 60]

            # Add current timestamp
            self.call_timestamps.append(now)

# Create a global rate limiter instance (increased for Pro version)
global_rate_limiter = GlobalRateLimiter(calls_per_minute=100)

# Perplexity data function
def perplexity_data(prompt_or_payload, system_prompt="Be precise and concise.", options=False, api_key=API_KEY):
    url = "https://api.perplexity.ai/chat/completions"

    # Check if prompt_or_payload is already a complete payload
    if isinstance(prompt_or_payload, dict) and "messages" in prompt_or_payload:
        payload = prompt_or_payload
    else:
        # Build payload from prompt
        payload = {
            # "model": "llama-3.1-sonar-huge-128k-online",
            "model": "sonar-reasoning",
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": prompt_or_payload
                }
            ],
            "temperature": 0.2,
            "top_p": 0.9,
            "return_citations": True,
           #"search_domain_filter": ["perplexity.ai"],
            "return_images": False,
            "return_related_questions": False,
            "search_recency_filter": "month",
            "top_k": 0,
            "stream": False,
            "presence_penalty": 0,
            "frequency_penalty": 1
        }

    if options:
        # join to payload
        payload.update({
                    "web_search_options": {
                    "search_context_size": "high"
                    }
                })
        print(payload)

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # Use synchronous request with SSL verification disabled if needed
    try:
        import ssl
        from urllib3.util import ssl_
        requests.packages.urllib3.disable_warnings()
        response = requests.post(
            url,
            json=payload,
            headers=headers,
            verify=False  # Disable SSL verification
        )
    except (ImportError, AttributeError):
        # Fall back to regular request if urllib3 isn't available or configured properly
        response = requests.post(url, json=payload, headers=headers)

    # Check if the request was successful
    if response.status_code == 200:
        response_data = response.json()
        try:
            # Extract the message content
            message_content = response_data['choices'][0]['message']['content']

            # If it's JSON format and response_format was specified, try to parse it
            if "response_format" in payload and payload["response_format"]["type"] == "json_schema":
                try:
                    return json.loads(message_content)
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse JSON from structured response: {message_content}")
                    return message_content
            else:
                # filter the <think> and <end_think> tags and everything in between
                message_content = re.sub(r'<think>.*?</think>', '', message_content)
                message_content = re.sub(r'<end_think>.*?</end_think>', '', message_content)
                message_content = message_content.replace("\n", "")

            return message_content
        except (KeyError, IndexError):
            return "Unexpected response format."
    else:
        return f"Request failed with status code: {response.status_code}, message: {response.text}"

# Create a rate limiter for the Perplexity API (limited to 50 RPM globally)
class PerplexityRateLimiter:
    def __init__(self, rate_limit=50, time_period=60):
        self.rate_limit = rate_limit  # Max requests per time period
        self.time_period = time_period  # Time period in seconds
        self.request_times = []  # Timestamps of recent requests
        self.lock = asyncio.Lock()  # Lock for thread safety
        self.semaphore = asyncio.Semaphore(10)  # Limit concurrent requests

    async def acquire(self):
        """Acquire permission to make an API call, respecting rate limits."""
        async with self.lock:
            # Remove timestamps older than the time period
            current_time = time.time()
            self.request_times = [t for t in self.request_times if current_time - t < self.time_period]

            # Check if we're at the rate limit
            while len(self.request_times) >= self.rate_limit:
                # Calculate time to wait until a slot opens up
                oldest_timestamp = min(self.request_times)
                wait_time = oldest_timestamp + self.time_period - current_time

                # If we need to wait, release the lock and wait
                if wait_time > 0:
                    logger.info(f"Rate limit reached, waiting {wait_time:.2f} seconds")
                    await asyncio.sleep(0.1)  # Small sleep to prevent CPU spinning

                    # Release and reacquire lock to let other tasks check
                    self.lock.release()
                    await asyncio.sleep(0.5)  # Give other tasks a chance
                    await self.lock.acquire()

                    # Update current time and clean request times again
                    current_time = time.time()
                    self.request_times = [t for t in self.request_times if current_time - t < self.time_period]
                else:
                    # No need to wait, a slot has opened up
                    break

            # Add the current request timestamp
            self.request_times.append(current_time)

            # Also acquire the semaphore to limit concurrent requests
            await self.semaphore.acquire()

    def release_semaphore(self):
        """Release the semaphore to allow another concurrent request."""
        try:
            self.semaphore.release()
        except ValueError:
            # Semaphore was likely already released
            pass

# Initialize the rate limiter (increased for Pro version)
perplexity_rate_limiter = PerplexityRateLimiter(rate_limit=100, time_period=60)

async def perplexity_api_with_rate_limit(payload, api_key=API_KEY):
    url = "https://api.perplexity.ai/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # Acquire rate limit permission
    await perplexity_rate_limiter.acquire()

    # Make the API call
    try:
        # Create SSL context with cert verification disabled if needed
        ssl_context = None
        try:
            import ssl
            ssl_context = ssl.create_default_context()
            # Uncomment the line below only if you're having certificate issues
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
        except ImportError:
            logger.warning("SSL module not available")

        connector = aiohttp.TCPConnector(ssl=ssl_context)

        # Set a timeout for the API call
        timeout = aiohttp.ClientTimeout(total=120)  # 120-second timeout (increased from 45 seconds)

        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    response_data = await response.json()
                    return response_data['choices'][0]['message']['content']
                elif response.status == 429:
                    # Rate limit hit, wait and try again
                    retry_after = int(response.headers.get('Retry-After', 10))
                    logger.warning(f"Rate limit hit, waiting {retry_after} seconds")
                    await asyncio.sleep(retry_after)
                    # Try again recursively after waiting
                    return await perplexity_api_with_rate_limit(payload, api_key)
                else:
                    error_text = await response.text()
                    raise Exception(f"Request failed with status code: {response.status}, message: {error_text}")
    except Exception as e:
        logger.error(f"Error in API call: {e}")
        raise e
    finally:
        # Always release the semaphore to prevent deadlocks
        perplexity_rate_limiter.release_semaphore()

# Helper functions for handling social media data
def extract_social_handles(influencer_data):
    """Extract social media handles from influencer data"""
    handles = {}

    # Check for common social media handle patterns in the data
    if isinstance(influencer_data, dict):
        # Try to find Instagram handle
        if "instagram" in influencer_data:
            handles["instagram"] = clean_handle(influencer_data["instagram"])
        elif "instagram_handle" in influencer_data:
            handles["instagram"] = clean_handle(influencer_data["instagram_handle"])
        elif "social_media" in influencer_data and isinstance(influencer_data["social_media"], dict):
            if "instagram" in influencer_data["social_media"]:
                sm_data = influencer_data["social_media"]["instagram"]
                if isinstance(sm_data, dict) and "username" in sm_data:
                    handles["instagram"] = clean_handle(sm_data["username"])

        # Try to find YouTube handle
        if "youtube" in influencer_data:
            handles["youtube"] = clean_handle(influencer_data["youtube"])
        elif "youtube_handle" in influencer_data:
            handles["youtube"] = clean_handle(influencer_data["youtube_handle"])
        elif "youtube_channel" in influencer_data:
            handles["youtube"] = clean_handle(influencer_data["youtube_channel"])
        elif "social_media" in influencer_data and isinstance(influencer_data["social_media"], dict):
            if "youtube" in influencer_data["social_media"]:
                sm_data = influencer_data["social_media"]["youtube"]
                if isinstance(sm_data, dict) and "username" in sm_data:
                    handles["youtube"] = clean_handle(sm_data["username"])

        # Try to find Facebook handle
        if "facebook" in influencer_data:
            handles["facebook"] = clean_handle(influencer_data["facebook"])
        elif "facebook_handle" in influencer_data:
            handles["facebook"] = clean_handle(influencer_data["facebook_handle"])
        elif "facebook_page" in influencer_data:
            handles["facebook"] = clean_handle(influencer_data["facebook_page"])
        elif "social_media" in influencer_data and isinstance(influencer_data["social_media"], dict):
            if "facebook" in influencer_data["social_media"]:
                sm_data = influencer_data["social_media"]["facebook"]
                if isinstance(sm_data, dict) and "username" in sm_data:
                    handles["facebook"] = clean_handle(sm_data["username"])

    return handles

def clean_handle(handle):
    """Clean social media handles by removing @ symbols, URLs, etc."""
    if not handle:
        return ""

    handle = str(handle).strip()

    # Remove @ symbol
    handle = handle.lstrip('@')

    # Remove URLs
    if "instagram.com/" in handle:
        handle = handle.split("instagram.com/")[-1].split("/")[0].split("?")[0]
    elif "youtube.com/" in handle:
        if "channel/" in handle:
            handle = handle.split("channel/")[-1].split("/")[0].split("?")[0]
        elif "user/" in handle:
            handle = handle.split("user/")[-1].split("/")[0].split("?")[0]
        elif "@" in handle:
            handle = handle.split("@")[-1].split("/")[0].split("?")[0]
    elif "facebook.com/" in handle:
        handle = handle.split("facebook.com/")[-1].split("/")[0].split("?")[0]

    # Remove common endings
    handle = re.sub(r'(\?.*|\/.*|#.*)$', '', handle)

    return handle

# Helper function to extract JSON from text responses
def extract_json_from_text(text, default_dict=None):
    """Attempt to extract a JSON object from text that might contain explanations before/after."""
    if not text:
        return default_dict or {}

    # Clean up text first by removing thinking tags and extra content
    clean_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    clean_text = re.sub(r'<end_think>.*?</end_think>', '', clean_text, flags=re.DOTALL)

    # Remove any explanations after JSON that might be breaking parsing
    # First try to find common patterns of JSON being followed by explanations
    if "}" in clean_text:
        # Try to find the last valid closing brace of a JSON object
        last_brace_match = re.search(r'(\{.*\})\s*[^{}]*$', clean_text, re.DOTALL)
        if last_brace_match:
            clean_text = last_brace_match.group(1)

    # First try to parse the entire text as JSON
    try:
        return json.loads(clean_text)
    except json.JSONDecodeError:
        pass

    # Try to find a JSON object in the text
    json_pattern = r'(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})'
    matches = re.findall(json_pattern, clean_text, re.DOTALL)

    if matches:
        # Sort matches by length, longest first (most likely to be complete)
        matches.sort(key=len, reverse=True)

        for match in matches:
            try:
                parsed = json.loads(match)
                return parsed
            except json.JSONDecodeError:
                continue

    # Try to find a JSON array in the text
    array_pattern = r'(\[[^\[\]]*(?:\[[^\[\]]*\][^\[\]]*)*\])'
    matches = re.findall(array_pattern, clean_text, re.DOTALL)

    if matches:
        # Sort matches by length, longest first
        matches.sort(key=len, reverse=True)

        for match in matches:
            try:
                parsed = json.loads(match)
                return {"result": parsed}  # Wrap array in an object
            except json.JSONDecodeError:
                continue

    # Last resort: try to find values with quotes which might be part of JSON
    if default_dict:
        # Look for key values in the format "key": "value" or "key": value
        extracted_values = {}

        # Check for each key in the default_dict
        for key in default_dict.keys():
            key_pattern = rf'"{key}"\s*:\s*("(?:[^"\\]|\\.)*"|null|\d+)'
            key_match = re.search(key_pattern, clean_text)
            if key_match:
                try:
                    # Extract the value part and convert it to proper JSON
                    value_str = key_match.group(1)
                    if value_str == "null":
                        extracted_values[key] = None
                    elif value_str.startswith('"'):
                        # It's a string, parse it
                        extracted_values[key] = json.loads(value_str)
                    else:
                        # It's a number
                        extracted_values[key] = int(value_str) if value_str.isdigit() else float(value_str)
                except (json.JSONDecodeError, ValueError):
                    # Keep the default value for this key
                    extracted_values[key] = default_dict[key]

        # If we extracted at least one value, merge with defaults and return
        if extracted_values:
            result = default_dict.copy()
            result.update(extracted_values)
            return result

    # If all else failed, return the default
    return default_dict or {}

# Social media details function
def social_media_details(social_media: str, influencer_name: str, product_category: str, location: str = ""):
    # Only log location if it's provided
    location_log = f" in {location}" if location else ""
    logger.info(f"{social_media} name search for {influencer_name}{location_log}")

    # Create a more natural location context for the query
    location_context = ""
    if location:
        location_context = f" in {location}"

    # Define the expected structure directly in the prompt
    expected_fields = """
    {
        "social_media": "Name of the platform",
        "followers": "Number of followers (integer or null)",
        "following": "Number of people they follow (integer or null)",
        "name": "Full name (string or null)",
        "username": "Account username (string or null)",
        "social_media_url": "Profile URL (string or null)",
        "average_engagements": "Average engagement count (integer or null)",
        "average_views": "Average view count (integer or null)",
        "potential_reach": "Potential reach (integer or null)",
        "potential_impressions": "Potential impressions (integer or null)"
    }
    """

    question = f"""
        What is the {social_media} username for {influencer_name}?
        What is the engagement rate for {influencer_name} on {social_media}?
        What is the potential reach on {social_media} for {influencer_name}?
        What are the average views for {influencer_name} on {social_media}?
        How many followers on {social_media} does {influencer_name} have?
        Note: this person is a popular {product_category} influencer{location_context}.

        Return ONLY a valid JSON object with this exact structure:
        {expected_fields}

        Fill in all fields with appropriate values or null if unknown. Return ONLY the JSON, nothing else.
    """

    payload = {
        "model": "sonar-reasoning",
        "messages": [
            {
                "role": "system",
                "content": "You are a social media data extraction assistant. You must return a valid JSON object with the exact specified structure. No explanations or additional text."
            },
            {
                "role": "user",
                "content": question
            }
        ],
        "temperature": 0.2,
        "top_p": 0.9,
        "return_citations": True,
       #"search_domain_filter": ["perplexity.ai"],
        "return_images": False,
        "return_related_questions": False,
        "search_recency_filter": "month",
        "top_k": 0,
        "stream": False,
        "presence_penalty": 0,
        "frequency_penalty": 1
        # Structured output format commented out as it's not available in current tier
        # "response_format": {
        #     "type": "json_schema",
        #     "json_schema": {"schema": social_media_schema}
        # }
    }

    # Default response structure
    default_response = {
        "social_media": social_media,
        "followers": None,
        "following": None,
        "name": influencer_name,
        "username": None,
        "social_media_url": None,
        "average_engagements": None,
        "average_views": None,
        "potential_reach": None,
        "potential_impressions": None
    }

    # Use perplexity_data function
    try:
        data = perplexity_data(payload)
        logger.info(f"Received response for {social_media} data for {influencer_name}")

        # Extract JSON from text response
        if isinstance(data, str):
            parsed_data = extract_json_from_text(data, default_response)

            # Ensure the social_media field is set
            if "social_media" not in parsed_data or not parsed_data["social_media"]:
                parsed_data["social_media"] = social_media

            # Ensure name is set if missing
            if "name" not in parsed_data or not parsed_data["name"]:
                parsed_data["name"] = influencer_name

            logger.info(f"Formatted Influencer {social_media} Data")
            return parsed_data
        else:
            logger.error(f"Unexpected non-string response: {type(data)}")
            return default_response

    except Exception as e:
        logger.error(f"Error getting {social_media} data for {influencer_name}: {e}")
        return default_response

# Contact details function
def contact_details(influencer_name: str, product_category: str, location: str = ""):
    # Only log location if it's provided
    location_log = f" in {location}" if location else ""
    logger.info(f"Getting contact details for {influencer_name}{location_log}")

    # Create a more natural location context for the query
    location_context = ""
    if location:
        location_context = f" in {location}"

    # Define the expected structure directly in the prompt
    expected_fields = """
    {
        "email": "Email address (string or null)",
        "phone": "Phone number (string or null)",
        "website": "Website URL (string or null)",
        "address": "Physical address (string or null)",
        "management_company": "Management company or agency name (string or null)",
        "manager_name": "Manager's name (string or null)",
        "manager_email": "Manager's email (string or null)",
        "manager_phone": "Manager's phone (string or null)"
    }
    """

    question = f"""
        What is the email address for {influencer_name}?
        What is the phone number for {influencer_name}?
        Does {influencer_name} have a website? If yes, what is it?
        What is the management company or agency that represents {influencer_name}?
        Who is the manager or agent for {influencer_name}?
        What is the email address for {influencer_name}'s manager or agent?
        What is the phone number for {influencer_name}'s manager or agent?
        Note: this person is a popular {product_category} influencer{location_context}.

        Return ONLY a valid JSON object with this exact structure:
        {expected_fields}

        Fill in all fields with appropriate values or null if unknown. Return ONLY the JSON, nothing else.
    """

    payload = {
        "model": "sonar-reasoning",
        "messages": [
            {
                "role": "system",
                "content": "You are a contact information extraction assistant. You must return a valid JSON object with the exact specified structure. No explanations or additional text."
            },
            {
                "role": "user",
                "content": question
            }
        ],
        "temperature": 0.2,
        "top_p": 0.9,
        "return_citations": True,
        "return_images": False,
        "return_related_questions": False,
        "search_recency_filter": "month",
        "top_k": 0,
       #"search_domain_filter": ["perplexity.ai"],
        "stream": False,
        "presence_penalty": 0,
        "frequency_penalty": 1
        # Structured output format commented out as it's not available in current tier
        # "response_format": {
        #     "type": "json_schema",
        #     "json_schema": {"schema": contact_schema}
        # }
    }

    # Default response structure
    default_response = {
        "email": None,
        "phone": None,
        "website": None,
        "address": None,
        "management_company": None,
        "manager_name": None,
        "manager_email": None,
        "manager_phone": None
    }

    # Use perplexity_data function
    try:
        data = perplexity_data(payload)

        # Extract JSON from text response
        if isinstance(data, str):
            parsed_data = extract_json_from_text(data, default_response)
            logger.info(f"Received contact data for {influencer_name}")
            return parsed_data
        else:
            logger.error(f"Unexpected non-string response: {type(data)}")
            return default_response

    except Exception as e:
        logger.error(f"Error getting contact details for {influencer_name}: {e}")
        return default_response

# Async social media details function
async def social_media_details_async(name, platform, location=""):
    """
    Get social media details for a specific platform.

    Args:
        name (str): Influencer name
        platform (str): Social media platform (instagram, youtube, facebook)
        location (str): Location context for search

    Returns:
        dict: Social media details
    """
    # Default fallback data structure that will be returned in case of errors
    fallback_data = {
        "error": True,
        "username": "",  # Don't generate fake usernames
        "name": name,
        "source": platform.lower(),
        "platform": platform.lower()
    }

    if not name or not platform:
        logger.warning(f"Missing parameters for social_media_details_async: name={name}, platform={platform}")
        return fallback_data

    # Wait for rate limiter
    await global_rate_limiter.wait()

    try:
        # Construct a question to get the handle
        location_context = f" in {location}" if location else ""
        question = f"What is the {platform} handle or username for {name}{location_context}?"

        logger.info(f"Getting {platform} username for {name}{location_context}")

        # First get the handle/username from Perplexity
        handle = ""  # Don't generate fake usernames

        # In a real implementation, you would call Perplexity API here to get the real handle
        # For example: handle = await get_username_from_perplexity(name, platform, location)

        # For now, we skip if no real handle is available
        logger.warning(f"No real {platform} handle available for {name} from Perplexity")
        return fallback_data

    except Exception as e:
        logger.error(f"Error in social_media_details_async for {name} on {platform}: {e}")
        return fallback_data

# Async contact details function
async def contact_details_async(name, location=""):
    """
    Get contact details for an influencer.

    Args:
        name (str): Influencer name
        location (str): Location context for search

    Returns:
        dict: Contact details
    """
    # Default contact structure to ensure consistency - no hardcoded data
    default_contact = {
        "email": "",
        "phone": "",
        "website": "",
        "address": "",
        "management_company": "",
        "manager_name": "",
        "manager_email": "",
        "manager_phone": "",
        "profile_image": ""
    }

    if not name:
        logger.warning("Missing name parameter for contact_details_async")
        return {
            "error": "Missing name parameter",
            "name": name,
            **default_contact
        }

    # Wait for rate limiter
    await global_rate_limiter.wait()

    try:
        # Construct a question to get contact details
        location_context = f" in {location}" if location else ""
        logger.info(f"Getting contact details for {name}{location_context}")

        # Get contact details using the perplexity_scraper
        try:
            contact_info = await get_contact_details(name)
            if not contact_info or not isinstance(contact_info, dict):
                contact_info = {
                    **default_contact,
                    "error": True
                }
            else:
                # Ensure all required fields are present
                for field in default_contact:
                    if field not in contact_info or contact_info[field] is None:
                        contact_info[field] = default_contact[field]
                contact_info["error"] = False
        except Exception as e:
            logger.error(f"Error getting contact details for {name}: {e}")
            contact_info = {
                **default_contact,
                "error": True
            }

        logger.info(f"Successfully fetched contact details for {name}")
        return contact_info

    except Exception as e:
        logger.error(f"Error in contact_details_async for {name}: {e}")
        return {
            "error": str(e),
            "name": name,
            **default_contact  # Include all default fields
        }

# Async fetch influencer data
async def fetch_influencer_data(name, category, location=""):
    """
    Fetch basic data for an influencer.

    Args:
        name (str): Influencer name
        category (str): Category of the influencer
        location (str): Location context for queries

    Returns:
        dict: Basic influencer data
    """
    if not name:
        logger.warning("Missing name parameter for fetch_influencer_data")
        return {
            "name": name,
            "error": "Missing name parameter",
            "followers": "",  # Only real data from Perplexity
            "category": category,
            "location": location if location else None,
            "description": f"Influencer in the {category} category",
            "engagement_rate": 0.0
        }

    # Wait for rate limiter
    await global_rate_limiter.wait()

    try:
        # Construct a question to get basic influencer data
        location_context = f" in {location}" if location else ""
        question = f"Who is {name}{location_context}? Provide information about their background, popularity, and what they do in the {category} category."

        # Here you would call Perplexity API to get the basic data
        # For now, we return empty data since we don't generate fake data
        return {
            "name": name,
            "category": category,
            "location": location if location else None,
            "description": f"Influencer in the {category} category",
            "followers": "",  # Only real data from Perplexity
            "engagement_rate": 0.0  # Only real data from Perplexity
        }
    except Exception as e:
        logger.error(f"Error in fetch_influencer_data for {name}: {e}")
        return {
            "name": name,
            "error": str(e),
            "followers": "0",  # Ensure followers is a string
            "category": category,
            "location": location if location else None,
            "description": f"Influencer in the {category} category",
            "engagement_rate": 0.0
        }

# Get all influencer data async
async def get_all_influencer_data(influencer_names, category, location=""):
    """
    Get basic data for multiple influencers.

    Args:
        influencer_names (list): List of influencer names
        category (str): Category of the influencers
        location (str): Location context for queries

    Returns:
        list: Basic influencer data
    """
    if not influencer_names:
        logger.warning("No influencer names provided to get_all_influencer_data")
        return []

    results = []

    for name in influencer_names:
        try:
            # Rate limiting removed - will only retry on actual API rate limit errors

            # Get basic influencer data without timeout to allow complete processing
            influencer_data = await fetch_influencer_data(name, category, location)

            results.append(influencer_data)

            # Add a small delay between influencers (reduced for Pro version)
            await asyncio.sleep(random.uniform(0.1, 0.2))

        except asyncio.TimeoutError:
            logger.error(f"Timeout fetching data for {name}")
            results.append({"name": name, "error": "Timeout"})
        except Exception as e:
            logger.error(f"Error fetching data for {name}: {e}")
            results.append({"name": name, "error": str(e)})

    return results

# Get social details async
async def get_social_details(influencers_data, location=""):
    """
    Enhance influencer data with social media details by first extracting handles and then fetching data.

    Args:
        influencers_data (list): List of influencer data dictionaries
        location (str): Location context for queries

    Returns:
        list: Enhanced influencer data with social media details
    """
    if not influencers_data:
        logger.warning("No influencer data provided to get_social_details")
        return []

    enhanced_data = []

    for influencer in influencers_data:
        try:
            # Make a copy of the influencer data
            enhanced_influencer = influencer.copy() if isinstance(influencer, dict) else {"name": str(influencer)}
            name = enhanced_influencer.get('name', 'Unknown Influencer')
            category = enhanced_influencer.get('category', 'Influencer')

            logger.info(f"Processing social media details for {name}")

            # Get social media handles for each platform (Instagram, YouTube, Facebook, TikTok)
            platforms = ["instagram", "youtube", "facebook", "tiktok"]
            results = {}

            for platform in platforms:
                # Add retry logic for each platform
                max_retries = 3
                retry_delay = 5  # seconds

                for retry_attempt in range(max_retries):
                    try:
                        # Step 1: Get the handle/username for this platform
                        # Rate limiting removed - will only retry on actual API rate limit errors

                        # Get platform details without timeout to allow complete processing
                        platform_details = await social_media_details_async(name, platform, location)

                        # Store the result
                        results[platform] = platform_details
                        logger.info(f"Got {platform} details for {name}")

                        # Add a small delay to avoid overwhelming APIs (reduced for Pro version)
                        await asyncio.sleep(random.uniform(0.1, 0.3))

                        # Success, break out of retry loop
                        break

                    except asyncio.TimeoutError:
                        logger.error(f"Timeout getting {platform} details for {name} (attempt {retry_attempt+1}/{max_retries})")

                        # If this is the last retry attempt, set the fallback data
                        # REMOVED: No longer setting followers_count to "0" on timeout
                        if retry_attempt == max_retries - 1:
                            results[platform] = {
                                "error": True,
                                "platform": platform,
                                "name": name,
                                "username": name.lower().replace(" ", ""),
                                "source": platform
                            }
                        else:
                            # Wait before retrying with exponential backoff
                            wait_time = retry_delay * (2 ** retry_attempt)
                            logger.info(f"Retrying in {wait_time} seconds...")
                            await asyncio.sleep(wait_time)

                    except Exception as e:
                        logger.error(f"Error getting {platform} details for {name} (attempt {retry_attempt+1}/{max_retries}): {e}")

                        # If this is the last retry attempt, set the fallback data
                        # REMOVED: No longer setting followers_count to "0" on error
                        if retry_attempt == max_retries - 1:
                            results[platform] = {
                                "error": True,
                                "platform": platform,
                                "name": name,
                                "username": name.lower().replace(" ", ""),
                                "source": platform
                            }
                        else:
                            # Wait before retrying with exponential backoff
                            wait_time = retry_delay * (2 ** retry_attempt)
                            logger.info(f"Retrying in {wait_time} seconds...")
                            await asyncio.sleep(wait_time)

            # Add social media results to enhanced influencer data
            enhanced_influencer["social_media"] = results

            # Calculate total followers from all platforms and ensure it's a string
            total_followers = 0
            for platform, data in results.items():
                if isinstance(data, dict) and "followers_count" in data:
                    try:
                        # Convert to int for calculation, handling both string and int values
                        followers = data["followers_count"]
                        if followers and isinstance(followers, str):
                            # Remove any commas or other formatting
                            followers = followers.replace(',', '').replace(' ', '')
                            if followers.isdigit():
                                total_followers += int(followers)
                        elif followers and isinstance(followers, (int, float)):
                            total_followers += int(followers)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error converting followers for {name} on {platform}: {e}")

            # Update the total followers count in the enhanced influencer data
            enhanced_influencer["followers"] = str(total_followers)

            # Step 3: Get contact details if not already present
            if not enhanced_influencer.get("contact"):
                # Add retry logic for contact details
                max_retries = 3
                retry_delay = 5  # seconds

                for retry_attempt in range(max_retries):
                    try:
                        # Rate limiting removed - will only retry on actual API rate limit errors

                        # Get contact details without timeout to allow complete processing
                        contact_info = await contact_details_async(name, location)

                        enhanced_influencer["contact"] = contact_info
                        logger.info(f"Got contact details for {name}")

                        # Success, break out of retry loop
                        break

                    except asyncio.TimeoutError:
                        logger.error(f"Timeout getting contact details for {name} (attempt {retry_attempt+1}/{max_retries})")

                        # If this is the last retry attempt, set the fallback data
                        if retry_attempt == max_retries - 1:
                            enhanced_influencer["contact"] = {
                                "error": "Timeout",
                                "name": name,
                                "email": f"contact@{name.lower().replace(' ', '')}.com",
                                "phone": None,
                                "website": f"https://www.{name.lower().replace(' ', '')}.com",
                                "address": None,
                                "management_company": None,
                                "manager_name": None,
                                "manager_email": None,
                                "manager_phone": None
                            }
                        else:
                            # Wait before retrying with exponential backoff
                            wait_time = retry_delay * (2 ** retry_attempt)
                            logger.info(f"Retrying contact details in {wait_time} seconds...")
                            await asyncio.sleep(wait_time)

                    except Exception as e:
                        logger.error(f"Error getting contact details for {name} (attempt {retry_attempt+1}/{max_retries}): {e}")

                        # If this is the last retry attempt, set the fallback data
                        if retry_attempt == max_retries - 1:
                            enhanced_influencer["contact"] = {
                                "error": str(e),
                                "name": name,
                                "email": f"contact@{name.lower().replace(' ', '')}.com",
                                "phone": None,
                                "website": f"https://www.{name.lower().replace(' ', '')}.com",
                                "address": None,
                                "management_company": None,
                                "manager_name": None,
                                "manager_email": None,
                                "manager_phone": None
                            }
                        else:
                            # Wait before retrying with exponential backoff
                            wait_time = retry_delay * (2 ** retry_attempt)
                            logger.info(f"Retrying contact details in {wait_time} seconds...")
                            await asyncio.sleep(wait_time)

            # Add the enhanced influencer data to results
            enhanced_data.append(enhanced_influencer)

            # Add a small delay between processing influencers (reduced for Pro version)
            await asyncio.sleep(random.uniform(0.1, 0.2))

        except Exception as e:
            logger.error(f"Error processing influencer {influencer.get('name', 'unknown')}: {e}")
            # Add the original influencer data to maintain the count
            enhanced_data.append(influencer)

    logger.info(f"Completed processing {len(enhanced_data)} influencers")
    return enhanced_data