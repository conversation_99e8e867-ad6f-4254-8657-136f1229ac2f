import sys, os
from fastapi import FastAPI, Form, Body, HTTPException, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from typing import List, Dict, Optional
import uvicorn
from pydantic import BaseModel
from utils import product_categorizer, product_categorizer_perplexity
from social_media_collection import (
    get_all_influencer_data,
    get_social_details,
    social_media_details_async,
    contact_details_async,
    extract_social_handles,
    clean_handle
)
from names_collection import get_all_names, get_category_influencers
import json
from asyncio import to_thread
import aiofiles
import uuid
import asyncio
from fastapi import BackgroundTasks
import logging
from datetime import datetime
from loguru import logger
import signal
import importlib.util

# No service initializations needed - using Perplexity only
async def init_instagram():
    logger.info("Instagram initialization skipped - using Perplexity only")
    pass

async def init_facebook():
    logger.info("Facebook initialization skipped - using Perplexity only")
    pass

YOUTUBE_API_KEY = None

# Enhanced social media functions removed - using Perplexity only
enhanced_functions_imported = False
logger.info("Enhanced social media functions not needed - using Perplexity only")

app = FastAPI()

# Function to import scraper modules
def import_scraper_modules():
    """Import the scraper modules needed for the API - PERPLEXITY ONLY."""
    try:
        # ONLY import from perplexity_scraper - no fallbacks to other scrapers
        global get_instagram_details, fetch_youtube_feed, get_facebook_details, get_tiktok_details, get_contact_details
        from perplexity_scraper import get_instagram_details, fetch_youtube_feed, get_facebook_details, get_tiktok_details, get_contact_details
        logger.info("Successfully imported scraper functions from perplexity_scraper")
        logger.info("All scrapers are now using Perplexity API exclusively")
        return True
    except ImportError as e:
        logger.error(f"CRITICAL: Could not import from perplexity_scraper: {e}")
        logger.error("All scrapers must use Perplexity API only. No fallback scrapers allowed.")
        logger.error("Please ensure perplexity_scraper.py is available and contains all required functions.")
        return False
    except Exception as e:
        logger.error(f"Error importing scraper modules: {e}")
        return False

# Initialize all services on startup
@app.on_event("startup")
async def startup_event():
    """Initialize all services on startup."""
    logger.info("Initializing services")

    # Import scraper modules
    if not import_scraper_modules():
        logger.error("Failed to import scraper modules. The API will not function correctly.")
    else:
        logger.info("Successfully imported scraper modules")

    # Initialize Instagram session
    try:
        logger.info("Initializing Instagram session")
        await init_instagram()
        logger.info("Instagram session initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing Instagram session: {e}")

    # Initialize Facebook session
    try:
        logger.info("Initializing Facebook session")
        await init_facebook()
        logger.info("Facebook session initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing Facebook session: {e}")

    # Check YouTube API key
    if YOUTUBE_API_KEY:
        logger.info("YouTube API key is available")
    else:
        logger.warning("YouTube API key is not available, YouTube data may be limited")

    logger.info("All services initialized")

# Define allowed origins for CORS
origins = ["*"]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger = logging.getLogger(__name__)

# Configure logging
# logger.add("api_server.log", rotation="1 MB")

class ProductRequest(BaseModel):
    products: List[str]

@app.post("/api/categorize-products/")
async def categorize_products(request: ProductRequest):
    categorized_output = product_categorizer_perplexity(request.products)
    return JSONResponse(content={"categorized_products": categorized_output})


class InfluencerData(BaseModel):
    influencer_names: List[str]
    category : str

@app.post("/api/influencer-data/")
async def influencers_data(request: InfluencerData):
    collected_data = await get_all_influencer_data(request.influencer_names, request.category)

    print(collected_data)

    # Filter and remove all "@" inside the string before parsing
    if isinstance(collected_data, list):
        collected_data = [data.replace("@", "") for data in collected_data]

    try:
        collected_data = [json.loads(data) for data in collected_data]
    except (TypeError, ValueError):
        print("Error parsing JSON data", TypeError, ValueError)
        pass
    collected_data = await get_social_details(collected_data)

    return JSONResponse(content={"categorized_products": collected_data})


class CategoryInfluencerData(BaseModel):
    category: str
    location: str = ""
    count: int = 5

class CategoryInfluencerRequest(BaseModel):
    category: str

class CategoriesRequest(BaseModel):
    categories: List[str]

# Create a directory for results if it doesn't exist
results_dir = os.path.join(os.path.dirname(__file__), "results")
os.makedirs(results_dir, exist_ok=True)

# Initialize tasks dictionary - Make it persistent
# We'll store this in memory but also write to disk for persistence
tasks = {}
tasks_file = os.path.join(results_dir, "tasks.json")

# Load existing tasks if available
try:
    if os.path.exists(tasks_file):
        with open(tasks_file, 'r') as f:
            tasks = json.load(f)
            logger.info(f"Loaded {len(tasks)} existing tasks from {tasks_file}")
except Exception as e:
    logger.error(f"Error loading tasks file: {e}")
    # Initialize empty if loading fails
    tasks = {}

# Helper function to save tasks
def save_tasks():
    try:
        # Log the current state of tasks
        logger.debug(f"Starting save_tasks operation with {len(tasks)} tasks")
        for task_id, task_info in tasks.items():
            logger.debug(f"Task {task_id} status: {task_info.get('status', 'unknown')}")

        # Make sure results directory exists
        os.makedirs(results_dir, exist_ok=True)

        # Create a copy of the tasks to save (to prevent modification during saving)
        tasks_to_save = {}
        for task_id, task_info in tasks.items():
            # Convert any objects that might not be JSON serializable
            task_copy = task_info.copy()
            # Ensure the result path is a string if it exists
            if 'result' in task_copy and task_copy['result'] is not None:
                task_copy['result'] = str(task_copy['result'])
            tasks_to_save[task_id] = task_copy

        # Log what we're about to save
        logger.info(f"Saving {len(tasks_to_save)} tasks to {tasks_file}")

        # Save with atomic write pattern
        tmp_file = f"{tasks_file}.tmp"
        with open(tmp_file, 'w') as f:
            json.dump(tasks_to_save, f, indent=2)
            f.flush()
            os.fsync(f.fileno())  # Ensure data is written to disk

        # Atomically replace the original file
        os.replace(tmp_file, tasks_file)

        logger.info(f"Successfully saved {len(tasks)} tasks to {tasks_file}")
    except Exception as e:
        logger.error(f"Error saving tasks file: {e}")
        # If there was an error, try a simple direct write as fallback
        try:
            logger.info("Attempting fallback save method")
            with open(tasks_file, 'w') as f:
                json.dump(tasks, f)
            logger.info(f"Saved {len(tasks)} tasks with fallback method")
        except Exception as e2:
            logger.error(f"Critical error: Failed to save tasks even with fallback: {e2}")

# Add a function to force save tasks
def force_save_tasks():
    """Force save tasks with additional logging and retries."""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            logger.info(f"Force saving tasks (attempt {attempt+1}/{max_retries})")

            # Write directly to avoid race conditions
            with open(tasks_file, 'w') as f:
                serializable_tasks = {}
                for task_id, task_info in tasks.items():
                    task_copy = task_info.copy()
                    if 'result' in task_copy and task_copy['result'] is not None:
                        task_copy['result'] = str(task_copy['result'])
                    serializable_tasks[task_id] = task_copy

                json.dump(serializable_tasks, f, indent=2)
                f.flush()
                os.fsync(f.fileno())

            logger.info(f"Force save successful, saved {len(tasks)} tasks")
            return True
        except Exception as e:
            logger.error(f"Force save attempt {attempt+1} failed: {e}")

    logger.critical("All force save attempts failed")
    return False

@app.get("/")
async def read_root():
    return {"message": "Welcome to the Influencer API", "version": "1.0"}

@app.post("/api/category-influencers")
async def get_influencers_for_category(request: CategoryInfluencerRequest):
    try:
        influencers = get_category_influencers(request.category)
        logger.info(f"Retrieved {len(influencers)} influencers for {request.category}")
        return {"category": request.category, "influencers": influencers}
    except Exception as e:
        logger.error(f"Error fetching influencers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/multi-category-influencers")
async def get_influencers_for_categories(request: CategoriesRequest):
    try:
        all_influencers = get_all_names(request.categories)
        logger.info(f"Retrieved influencers for {len(request.categories)} categories")
        return all_influencers
    except Exception as e:
        logger.error(f"Error fetching influencers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

from asyncio import to_thread
import aiofiles

@app.post("/api/start-category-influencer-data")
async def start_category_influencers_data(request: CategoryInfluencerData):
    task_id = str(uuid.uuid4())
    # Initialize the task state
    tasks[task_id] = {"status": "in_progress", "result": None}

    # Save tasks immediately after creating a new one
    force_save_tasks()

    # Log task creation
    logger.info(f"Created new task {task_id} for category {request.category}, location {request.location}")
    logger.info(f"Current tasks: {list(tasks.keys())}")

    # Schedule the task in the asyncio event loop with proper error handling
    # Name the task with the task_id for easier tracking
    background_task = asyncio.create_task(
        fetch_and_store_influencer_data(task_id, request.category, request.location, request.count)
    )
    background_task.set_name(f"influencer_task_{task_id}")

    # Set up a callback to handle task completion or failure
    background_task.add_done_callback(lambda t: handle_task_completion(t, task_id))

    # Return the task ID immediately to the client
    return JSONResponse(content={"task_id": task_id})

def handle_task_completion(task, task_id):
    """Handle task completion or failure"""
    try:
        # Check if the task raised an exception
        if task.exception():
            exception = task.exception()
            logger.error(f"Task {task_id} raised an exception: {exception}")

            # Check if task still exists
            if task_id in tasks:
                tasks[task_id] = {"status": "failed", "error": str(exception)}
                logger.info(f"Updated task {task_id} status to failed due to exception")
                force_save_tasks()
            else:
                logger.error(f"Task {task_id} not found in tasks dictionary during completion handling")
                # Re-add the task
                tasks[task_id] = {"status": "failed", "error": str(exception)}
                logger.info(f"Re-added task {task_id} with failed status")
                force_save_tasks()
        else:
            logger.info(f"Task {task_id} completed without raising exceptions")

            # Check if task is already marked as completed in the dictionary
            if task_id in tasks:
                status = tasks[task_id].get("status", "unknown")

                # If the task is marked as failed, don't change it to completed
                if status == "failed":
                    logger.info(f"Task {task_id} completed but was marked as failed. Keeping failed status.")

                    # Make sure there's an error message
                    if "error" not in tasks[task_id]:
                        tasks[task_id]["error"] = "Task failed but no specific error was provided"

                    # Add a default result for failed tasks
                    if "result" not in tasks[task_id]:
                        tasks[task_id]["result"] = {
                            "metadata": {
                                "task_id": task_id,
                                "status": "failed"
                            },
                            "influencers": []
                        }

                    force_save_tasks()
                elif status != "completed":
                    logger.warning(f"Task {task_id} completed but has status {status} in dictionary")

                    # Only update if not already completed (this avoids overwriting a valid completion)
                    logger.info(f"Setting task {task_id} status to completed in completion handler")
                    tasks[task_id]["status"] = "completed"

                    # Make sure there's a result
                    if "result" not in tasks[task_id]:
                        tasks[task_id]["result"] = {
                            "metadata": {
                                "task_id": task_id,
                                "status": "completed"
                            },
                            "influencers": []
                        }

                    force_save_tasks()
            else:
                logger.error(f"Task {task_id} not found in tasks dictionary during successful completion")
                # In this case, we don't know the result path, so we can only mark as completed
                tasks[task_id] = {
                    "status": "completed",
                    "result": {
                        "metadata": {
                            "task_id": task_id,
                            "status": "completed"
                        },
                        "influencers": []
                    }
                }
                logger.info(f"Re-added task {task_id} with completed status and empty result")
                force_save_tasks()

    except Exception as e:
        logger.error(f"Error handling task completion for {task_id}: {e}")
        # Make sure we don't lose the task
        if task_id not in tasks:
            tasks[task_id] = {
                "status": "unknown",
                "error": f"Error in completion handler: {str(e)}",
                "result": {
                    "metadata": {
                        "task_id": task_id,
                        "status": "unknown"
                    },
                    "influencers": []
                }
            }
        else:
            tasks[task_id]["error"] = f"Error in completion handler: {str(e)}"
            if "result" not in tasks[task_id]:
                tasks[task_id]["result"] = {
                    "metadata": {
                        "task_id": task_id,
                        "status": tasks[task_id].get("status", "unknown")
                    },
                    "influencers": []
                }
        force_save_tasks()

async def get_social_details_with_real_handles(influencers_data, discovery_data, location=""):
    """
    Enhanced version of get_social_details that uses real social media handles from discovery data.

    Args:
        influencers_data (list): List of influencer data dictionaries
        discovery_data (dict): Dictionary mapping influencer names to their discovery data with real handles
        location (str): Location context for queries

    Returns:
        list: Enhanced influencer data with social media details using real handles
    """
    if not influencers_data:
        logger.warning("No influencer data provided to get_social_details_with_real_handles")
        return []

    enhanced_data = []

    for influencer in influencers_data:
        try:
            # Make a copy of the influencer data
            enhanced_influencer = influencer.copy() if isinstance(influencer, dict) else {"name": str(influencer)}
            name = enhanced_influencer.get('name', 'Unknown Influencer')

            logger.info(f"Processing social media details for {name} with real handles")

            # Get the discovery data for this influencer
            discovery_info = discovery_data.get(name, {})
            actual_handles = discovery_info.get('actual_handles', {})

            if actual_handles:
                logger.info(f"Found real handles for {name}: {actual_handles}")
            else:
                logger.warning(f"No real handles found for {name}, using fallback method")

            # Get social media handles for each platform using real handles
            platforms = ["instagram", "youtube", "facebook", "tiktok"]
            results = {}

            for platform in platforms:
                try:
                    # Get the real handle for this platform
                    real_handle = actual_handles.get(platform, "")
                    if real_handle:
                        logger.info(f"Using real {platform} handle for {name}: @{real_handle}")
                        handle = real_handle
                    else:
                        logger.info(f"No real {platform} handle for {name}, attempting to find via Perplexity search")
                        # Instead of skipping, try to find the handle using the influencer's name
                        handle = name  # Use the influencer's name to search for their platform presence

                    # Get platform details using the real handle
                    platform_details = await get_platform_details_with_fallback(name, platform, handle)
                    results[platform] = platform_details
                    logger.info(f"Got {platform} details for {name}")

                    # Add a small delay to avoid overwhelming APIs
                    await asyncio.sleep(0.2)

                except Exception as e:
                    logger.error(f"Error getting {platform} details for {name}: {e}")
                    # Set error data without generating fake usernames
                    # REMOVED: No longer setting followers_count to "0" on error
                    results[platform] = {
                        "error": True,
                        "platform": platform,
                        "name": name,
                        "username": "",
                        "source": platform,
                        "message": f"Error retrieving {platform} data: {str(e)}"
                    }

            # Add social media results to enhanced influencer data
            enhanced_influencer["social_media"] = results

            # Calculate total followers from all platforms
            total_followers = 0
            for platform, data in results.items():
                if isinstance(data, dict) and "followers_count" in data:
                    try:
                        followers = data["followers_count"]
                        if followers and isinstance(followers, str):
                            followers = followers.replace(',', '').replace(' ', '')
                            if followers.isdigit():
                                total_followers += int(followers)
                        elif followers and isinstance(followers, (int, float)):
                            total_followers += int(followers)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error converting followers for {name} on {platform}: {e}")

            # Update the total followers count
            enhanced_influencer["followers"] = str(total_followers)

            # Get contact details (using existing function)
            try:
                contact_info = await get_contact_details(name)
                enhanced_influencer["contact"] = contact_info
                logger.info(f"Got contact details for {name}")
            except Exception as e:
                logger.error(f"Error getting contact details for {name}: {e}")
                enhanced_influencer["contact"] = {
                    "error": True,
                    "name": name,
                    "email": "",
                    "phone": "",
                    "website": "",
                    "address": "",
                    "management_company": "",
                    "manager_name": "",
                    "manager_email": "",
                    "manager_phone": "",
                    "profile_image": "",
                    "message": f"Error retrieving contact details: {str(e)}"
                }

            enhanced_data.append(enhanced_influencer)

            # Add delay between influencers
            await asyncio.sleep(0.1)

        except Exception as e:
            logger.error(f"Error processing influencer {influencer.get('name', 'unknown')}: {e}")
            enhanced_data.append(influencer)

    logger.info(f"Completed processing {len(enhanced_data)} influencers with real handles")
    return enhanced_data

async def get_platform_details_with_fallback(influencer_name, platform, username):
    """
    Get platform details using ONLY direct Perplexity queries.
    NO fallback mechanisms, NO hardcoded data.

    Args:
        influencer_name (str): The name of the influencer
        platform (str): The social media platform (instagram, youtube, facebook, tiktok)
        username (str): The username to use for the query

    Returns:
        dict: Platform details from Perplexity only
    """
    logger.info(f"Getting {platform} details for {influencer_name} (username: {username}) - DIRECT Perplexity only")

    try:
        # Call the appropriate scraper based on platform - ALL use direct Perplexity only
        if platform.lower() == "instagram":
            platform_data = await get_instagram_details(username)
        elif platform.lower() == "youtube":
            platform_data = await fetch_youtube_feed(username)
        elif platform.lower() == "facebook":
            platform_data = await get_facebook_details(username)
        elif platform.lower() == "tiktok" and "get_tiktok_details" in globals():
            platform_data = await get_tiktok_details(username)
        else:
            logger.warning(f"{platform} scraper not available for {username}")
            return {
                "error": True,
                "username": username,
                "name": influencer_name,
                "source": platform.lower()
            }

        # Return exactly what Perplexity provides - no modifications
        logger.info(f"Direct Perplexity data for {platform} - {username}: {platform_data.get('followers_count' if platform != 'youtube' else 'subscribers', '0')}")
        return platform_data

    except Exception as e:
        logger.error(f"Error getting {platform} data for {username}: {e}")
        return {
            "error": True,
            "username": username,
            "name": influencer_name,
            "source": platform.lower()
        }

async def fetch_and_store_influencer_data(task_id, category, location, count=5):
    """Fetch and store influencer data for a category in the background."""
    try:
        logger.info(f"Starting task {task_id} for {category} influencers{' in '+location if location else ''}")

        # Make sure the task exists in the tasks dictionary
        if task_id not in tasks:
            logger.error(f"Task {task_id} not found in tasks dictionary")
            return

        # Get influencer names with actual social media handles using improved discovery
        try:
            logger.info(f"Getting {count} influencer names for {category}{' in '+location if location else ''}")

            # Import our improved discovery function
            from perplexity_scraper import discover_influencers

            # Use the improved discovery function that gets real social media handles
            discovered_influencers = await discover_influencers(category, location, count)

            if discovered_influencers:
                # Extract just the names for compatibility with existing workflow
                influencer_names = [inf['name'] for inf in discovered_influencers]

                # Store the full discovery data for later use
                discovery_data = {inf['name']: inf for inf in discovered_influencers}

                logger.info(f"Found {len(influencer_names)} influencers with real handles for {category}{' in '+location if location else ''}")
            else:
                # NO FALLBACK - if discovery fails, return error
                logger.error("Discovery with real handles failed - no fallback method used")
                tasks[task_id] = {
                    "status": "failed",
                    "error": "Discovery failed - no influencers found",
                    "result": {
                        "metadata": {
                            "category": category,
                            "location": location,
                            "count": 0,
                            "task_id": task_id,
                            "status": "failed"
                        },
                        "influencers": []
                    }
                }
                force_save_tasks()
                return
        except Exception as e:
            logger.error(f"Error getting influencer names: {e}")
            tasks[task_id] = {
                "status": "failed",
                "error": f"Failed to get influencer names: {str(e)}",
                "result": {
                    "metadata": {
                        "category": category,
                        "location": location,
                        "count": 0,
                        "task_id": task_id,
                        "status": "failed"
                    },
                    "influencers": []
                }
            }
            force_save_tasks()
            return

        # Log the number of influencers we got
        logger.info(f"Retrieved {len(influencer_names)} influencers as requested")

        # Exit early if no influencers found
        if not influencer_names:
            logger.warning(f"No influencers found for {category}{' in '+location if location else ''}")
            tasks[task_id] = {
                "status": "failed",
                "error": "No influencers found",
                "result": {
                    "metadata": {
                        "category": category,
                        "location": location,
                        "count": 0,
                        "task_id": task_id,
                        "status": "failed"
                    },
                    "influencers": []
                }
            }
            force_save_tasks()
            return

        # Make sure task still exists
        if task_id not in tasks:
            logger.error(f"Task {task_id} no longer exists in tasks dictionary. Recreating.")
            tasks[task_id] = {"status": "in_progress", "result": None}
            force_save_tasks()

        # Fetch basic data for all influencers without timeout to allow complete processing
        try:
            logger.info(f"Fetching basic data for {len(influencer_names)} influencers")
            influencers_data = await get_all_influencer_data(influencer_names, category, location)
            logger.info(f"Fetched basic data for {len(influencers_data)} influencers")
        except Exception as e:
            logger.error(f"Error getting basic data: {e}")
            tasks[task_id] = {
                "status": "failed",
                "error": f"Failed to get basic data: {str(e)}",
                "result": {
                    "metadata": {
                        "category": category,
                        "location": location,
                        "count": len(influencer_names),
                        "task_id": task_id,
                        "status": "failed"
                    },
                    "influencers": []
                }
            }
            force_save_tasks()
            return

        # Exit early if no basic data
        if not influencers_data:
            logger.warning(f"No basic data retrieved for {category}")
            tasks[task_id] = {
                "status": "failed",
                "error": "No basic data retrieved",
                "result": {
                    "metadata": {
                        "category": category,
                        "location": location,
                        "count": len(influencer_names),
                        "task_id": task_id,
                        "status": "failed"
                    },
                    "influencers": []
                }
            }
            force_save_tasks()
            return

        # Get social details with timeout protection using real handles if available
        try:
            logger.info(f"Getting social details for {len(influencers_data)} influencers")

            # Use enhanced social details function if we have discovery data with real handles
            if 'discovery_data' in locals() and discovery_data:
                logger.info("Using enhanced social details with real handles")
                enhanced_data = await asyncio.wait_for(
                    get_social_details_with_real_handles(influencers_data, discovery_data, location),
                    timeout=1800  # 30 minute timeout (increased from 20 minutes)
                )
            else:
                logger.info("Using standard social details (no real handles available)")
                enhanced_data = await asyncio.wait_for(
                    get_social_details(influencers_data, location),
                    timeout=1800  # 30 minute timeout (increased from 20 minutes)
                )
            logger.info(f"Enhanced data with social details for {len(enhanced_data)} influencers")

            # Enhanced social media functions removed - using Perplexity only
            logger.info("Using Perplexity-only data, no additional enhancement needed")

        except asyncio.TimeoutError:
            logger.error(f"Timeout getting social details for {category}")
            tasks[task_id] = {
                "status": "failed",
                "error": "Timeout getting social media details",
                "result": {
                    "metadata": {
                        "category": category,
                        "location": location,
                        "count": len(influencer_names),
                        "task_id": task_id,
                        "status": "failed"
                    },
                    "influencers": []
                }
            }
            force_save_tasks()
            return
        except Exception as e:
            logger.error(f"Error getting social details: {e}")
            tasks[task_id] = {
                "status": "failed",
                "error": f"Failed to get social details: {str(e)}",
                "result": {
                    "metadata": {
                        "category": category,
                        "location": location,
                        "count": len(influencer_names),
                        "task_id": task_id,
                        "status": "failed"
                    },
                    "influencers": []
                }
            }
            force_save_tasks()
            return

        # Exit early if no enhanced data
        if not enhanced_data:
            logger.warning(f"No enhanced data retrieved for {category}")
            tasks[task_id] = {
                "status": "failed",
                "error": "No enhanced data retrieved",
                "result": {
                    "metadata": {
                        "category": category,
                        "location": location,
                        "count": len(influencer_names),
                        "task_id": task_id,
                        "status": "failed"
                    },
                    "influencers": []
                }
            }
            force_save_tasks()
            return

        # Create results file path
        filename = f"{category.replace(' ', '_').lower()}_{uuid.uuid4()}.json"
        result_file = os.path.join(results_dir, filename)

        # Store result with metadata
        result = {
            "metadata": {
                "category": category,
                "location": location,
                "count": len(enhanced_data),
                "task_id": task_id
            },
            "influencers": enhanced_data
        }

        # Ensure the results directory exists
        os.makedirs(os.path.dirname(result_file), exist_ok=True)

        # Save results
        try:
            logger.info(f"Saving results to {result_file}")
            async with aiofiles.open(result_file, "w") as f:
                await f.write(json.dumps(result, indent=2))
                await f.flush()

            logger.info(f"Successfully wrote results to {result_file}")

            # Verify the file exists
            if not os.path.exists(result_file):
                raise FileNotFoundError(f"File {result_file} was not created")

            # Verify file size
            file_size = os.path.getsize(result_file)
            logger.info(f"Result file size: {file_size} bytes")
            if file_size == 0:
                raise ValueError("Result file is empty")

        except Exception as e:
            logger.error(f"Error saving results: {e}")
            tasks[task_id] = {"status": "failed", "error": f"Failed to save results: {str(e)}"}
            force_save_tasks()
            return

        # Update task status and save both the result file path and the actual result data
        logger.info(f"Updating task {task_id} status to completed")
        tasks[task_id] = {
            "status": "completed",
            "result": result,  # Store the actual result data
            "result_file": result_file  # Also store the file path for reference
        }

        # Force save tasks to ensure persistence
        logger.info(f"About to save task {task_id} completion status")
        success = force_save_tasks()
        if success:
            logger.info(f"Successfully saved task {task_id} completion status")
        else:
            logger.error(f"Failed to save task {task_id} completion status")

        logger.info(f"Task {task_id} completed successfully, saved to {result_file}")
        logger.info(f"Current tasks after completion: {list(tasks.keys())}")

    except Exception as e:
        logger.error(f"Error in task {task_id}: {e}")
        tasks[task_id] = {"status": "failed", "error": str(e)}

        # Save tasks to persist the failed status
        success = force_save_tasks()
        if not success:
            logger.critical(f"Failed to save failed status for task {task_id}")

    finally:
        # Log task completion for debugging
        logger.info(f"fetch_and_store_influencer_data function exiting for {task_id}")
        # One final attempt to save the task status
        if task_id in tasks:
            logger.info(f"Final task status for {task_id}: {tasks[task_id].get('status', 'unknown')}")
        else:
            logger.error(f"Task {task_id} not in tasks dictionary at end of function")

@app.get("/api/check-task-status/{task_id}")
async def check_task_status(task_id: str):
    # Log the request for debugging
    logger.info(f"Checking status for task {task_id}")
    logger.info(f"Current tasks in memory: {list(tasks.keys())}")

    # First check in memory
    if task_id in tasks:
        status = tasks[task_id]["status"]
        logger.info(f"Found task {task_id} in memory with status: {status}")
        return JSONResponse(content={"task_id": task_id, "status": status})

    # If not in memory, try loading from file
    logger.warning(f"Task ID {task_id} not found in memory, trying to load from file")
    loaded_task = None

    try:
        if os.path.exists(tasks_file):
            logger.info(f"Reading tasks file: {tasks_file}")
            with open(tasks_file, 'r') as f:
                try:
                    file_content = f.read()
                    if not file_content.strip():
                        logger.error(f"Tasks file is empty: {tasks_file}")
                        return JSONResponse(content={"error": "Tasks file is empty"}, status_code=500)

                    loaded_tasks = json.loads(file_content)
                    logger.info(f"Loaded {len(loaded_tasks)} tasks from file")

                    if task_id in loaded_tasks:
                        # Add this task back to our in-memory tasks
                        loaded_task = loaded_tasks[task_id]
                        tasks[task_id] = loaded_task
                        logger.info(f"Loaded task {task_id} from file with status: {loaded_task.get('status')}")
                        return JSONResponse(content={"task_id": task_id, "status": loaded_task.get("status")})
                    else:
                        logger.warning(f"Task {task_id} not found in tasks file")
                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing tasks file: {e}")
                    # Try to read the file raw to see what's in it
                    try:
                        with open(tasks_file, 'r') as raw_f:
                            raw_content = raw_f.read(1000)  # Read first 1000 chars for debugging
                            logger.error(f"Raw file content (first 1000 chars): {raw_content}")
                    except Exception as raw_error:
                        logger.error(f"Error reading raw file: {raw_error}")
        else:
            logger.error(f"Tasks file not found: {tasks_file}")
    except Exception as e:
        logger.error(f"Error loading tasks from file: {e}")

    # Check all running tasks in the asyncio event loop
    try:
        import asyncio
        all_tasks = asyncio.all_tasks()
        task_names = [t.get_name() for t in all_tasks]
        logger.info(f"Current asyncio tasks: {task_names}")

        # Check if any task names contain our task_id
        matching_tasks = [t for t in all_tasks if task_id in t.get_name()]
        if matching_tasks:
            logger.info(f"Found {len(matching_tasks)} matching asyncio tasks for {task_id}")
            # Task is running but not in our dictionary, add it
            if task_id not in tasks:
                tasks[task_id] = {"status": "in_progress", "result": None}
                logger.info(f"Re-added task {task_id} to tasks dictionary with in_progress status")
                force_save_tasks()
            return JSONResponse(content={"task_id": task_id, "status": "in_progress"})
    except Exception as e:
        logger.error(f"Error checking asyncio tasks: {e}")

    logger.warning(f"Task ID {task_id} not found anywhere")
    return JSONResponse(content={"error": "Task ID not found"}, status_code=404)

# Function to transform influencer data to the desired format
def transform_influencer_data(influencer_data):
    """
    Transform influencer data to match the desired response format.

    Args:
        influencer_data (dict): Original influencer data

    Returns:
        dict: Transformed influencer data
    """
    transformed_influencers = []

    # Log the transformation process
    logger.info(f"Transforming influencer data for {len(influencer_data.get('influencers', []))} influencers")

    for influencer in influencer_data.get("influencers", []):
        # Calculate total followers from all platforms
        total_followers = 0
        social_media = influencer.get("social_media", {})

        # Helper function to parse follower counts
        def parse_follower_count(count_str):
            if not count_str or count_str == "0":
                return 0

            # Handle decimal values in M and K formats
            count_str = str(count_str).strip()

            try:
                if 'M' in count_str:
                    # Handle decimal millions (e.g., "1.5M")
                    count_str = count_str.replace('M', '')
                    return int(float(count_str) * 1000000)
                elif 'K' in count_str:
                    # Handle decimal thousands (e.g., "500.5K")
                    count_str = count_str.replace('K', '')
                    return int(float(count_str) * 1000)
                else:
                    # Handle plain numbers
                    return int(count_str)
            except (ValueError, TypeError):
                logger.warning(f"Could not parse follower count: {count_str}")
                return 0

        # Add Instagram followers
        if "instagram" in social_media:
            instagram_followers = social_media["instagram"].get("followers_count", "0")
            total_followers += parse_follower_count(instagram_followers)

        # Add YouTube subscribers
        if "youtube" in social_media:
            youtube_subscribers = social_media["youtube"].get("subscribers", "0")
            if not youtube_subscribers or youtube_subscribers == "0":
                youtube_subscribers = social_media["youtube"].get("followers_count", "0")
            total_followers += parse_follower_count(youtube_subscribers)

        # Add Facebook followers
        if "facebook" in social_media:
            facebook_followers = social_media["facebook"].get("followers_count", "0")
            total_followers += parse_follower_count(facebook_followers)

        # Add TikTok followers
        if "tiktok" in social_media:
            tiktok_followers = social_media["tiktok"].get("followers_count", "0")
            total_followers += parse_follower_count(tiktok_followers)

        # Format the total followers count
        followers_str = str(total_followers)
        if total_followers >= 1000000:
            followers_str = f"{total_followers/1000000:.2f}M".replace(".00", "")
        elif total_followers >= 1000:
            followers_str = f"{total_followers/1000:.2f}K".replace(".00", "")

        # Create the base influencer structure
        transformed_influencer = {
            "name": influencer.get("name", ""),
            "category": influencer.get("category", ""),
            "location": influencer.get("location", ""),
            "description": influencer.get("description", ""),
            "followers": followers_str,  # Use the calculated total followers
            "total_followers": followers_str,  # Add total_followers field for clarity
            "engagement_rate": influencer.get("engagement_rate", 0),
            "image": None  # Initialize image field
        }

        # If the influencer already has a total_followers field, use it
        if "total_followers" in influencer and influencer["total_followers"]:
            transformed_influencer["total_followers"] = influencer["total_followers"]

        # Try to get an image URL from Perplexity data
        social_media = influencer.get("social_media", {})

        # Try to get image from Instagram first (usually highest quality)
        if "instagram" in social_media and social_media["instagram"].get("profile_image_url"):
            transformed_influencer["image"] = social_media["instagram"].get("profile_image_url")
        # Then try Instagram's profile_image field (for backward compatibility)
        elif "instagram" in social_media and social_media["instagram"].get("profile_image"):
            transformed_influencer["image"] = social_media["instagram"].get("profile_image")
        # Then try TikTok
        elif "tiktok" in social_media and social_media["tiktok"].get("profile_image_url"):
            transformed_influencer["image"] = social_media["tiktok"].get("profile_image_url")
        # Then try TikTok's profile_image field (for backward compatibility)
        elif "tiktok" in social_media and social_media["tiktok"].get("profile_image"):
            transformed_influencer["image"] = social_media["tiktok"].get("profile_image")
        # Then try Facebook
        elif "facebook" in social_media and social_media["facebook"].get("profile_image_url"):
            transformed_influencer["image"] = social_media["facebook"].get("profile_image_url")
        # Then try Facebook's image field (for backward compatibility)
        elif "facebook" in social_media and social_media["facebook"].get("image"):
            transformed_influencer["image"] = social_media["facebook"].get("image")
        # Then try YouTube
        elif "youtube" in social_media and social_media["youtube"].get("profile_image_url"):
            transformed_influencer["image"] = social_media["youtube"].get("profile_image_url")
        # Then try YouTube's thumbnail_url field (for backward compatibility)
        elif "youtube" in social_media and social_media["youtube"].get("thumbnail_url"):
            transformed_influencer["image"] = social_media["youtube"].get("thumbnail_url")
        # Finally try contact information
        elif "contact" in influencer and influencer["contact"].get("profile_image"):
            transformed_influencer["image"] = influencer["contact"].get("profile_image")

        # Extract social media handles
        social_media = influencer.get("social_media", {})

        # Instagram handle
        if "instagram" in social_media:
            instagram_data = social_media["instagram"]
            transformed_influencer["instagram_handle"] = {
                "username": instagram_data.get("username", ""),
                "name": instagram_data.get("name", ""),
                "Instagram_followers": instagram_data.get("followers_count", instagram_data.get("Instagram_followers", "0"))
                # Removed image field
            }

        # YouTube handle
        if "youtube" in social_media:
            youtube_data = social_media["youtube"]
            # Get subscribers from any available field
            youtube_subscribers = "0"
            for field in ["subscribers", "Youtube_subscribers", "followers_count"]:
                if field in youtube_data and youtube_data[field] and youtube_data[field] != "0":
                    youtube_subscribers = youtube_data[field]
                    break

            transformed_influencer["youtube_handle"] = {
                "title": youtube_data.get("title", ""),
                "link": youtube_data.get("link", ""),
                "id": youtube_data.get("id", ""),
                "source": youtube_data.get("source", "youtube"),
                "username": youtube_data.get("username", ""),
                "description": youtube_data.get("description", ""),
                "Youtube_subscribers": youtube_subscribers,
                "total_views": youtube_data.get("total_views", "0"),
                "total_videos": youtube_data.get("total_videos", "0"),
                "name": youtube_data.get("name", "")
                # Removed image field
            }

            # Format YouTube videos as an array of links (limited to 10)
            if "videos" in youtube_data and isinstance(youtube_data["videos"], list):
                videos = youtube_data["videos"]
                video_links = []

                for video in videos[:10]:  # Limit to 10 videos
                    if isinstance(video, dict) and "link" in video:
                        video_links.append(video["link"])
                    elif isinstance(video, str):
                        video_links.append(video)

                transformed_influencer["youtube_handle"]["videos"] = video_links

        # Facebook handle
        if "facebook" in social_media:
            facebook_data = social_media["facebook"]
            # Get followers from any available field
            facebook_followers = "0"
            for field in ["followers_count", "Facebook_followers"]:
                if field in facebook_data and facebook_data[field] and facebook_data[field] != "0":
                    facebook_followers = facebook_data[field]
                    break

            transformed_influencer["facebook_handle"] = {
                "username": facebook_data.get("username", ""),
                "url": facebook_data.get("url", ""),
                "source": facebook_data.get("source", "facebook"),
                "title": facebook_data.get("title", "Facebook"),
                "name": facebook_data.get("name", ""),
                "Facebook_followers": facebook_followers
            }
            # Don't include canonical_url or image

        # TikTok handle
        if "tiktok" in social_media:
            tiktok_data = social_media["tiktok"]
            # Get followers from any available field
            tiktok_followers = "0"
            for field in ["followers_count", "Tiktok_followers"]:
                if field in tiktok_data and tiktok_data[field] and tiktok_data[field] != "0":
                    tiktok_followers = tiktok_data[field]
                    break

            transformed_influencer["tiktok_handle"] = {
                "username": tiktok_data.get("username", ""),
                "link": tiktok_data.get("link", ""),
                "source": tiktok_data.get("source", "tiktok"),
                "name": tiktok_data.get("name", ""),
                "Tiktok_followers": tiktok_followers,
                "bio": tiktok_data.get("bio", "")
                # Removed image field
            }

        # Contact information
        if "contact" in influencer:
            contact_data = influencer["contact"]
            # Replace None values with empty strings
            contact_info = {}
            for field in contact_data:
                if contact_data[field] is None:
                    contact_info[field] = ""
                else:
                    contact_info[field] = contact_data[field]

            # Ensure all required fields are present
            required_fields = [
                "name", "email", "phone", "website", "address",
                "management_company", "manager_name", "manager_email",
                "manager_phone"
                # Removed profile_image from required fields as it's handled at the top level
            ]

            for field in required_fields:
                if field not in contact_info:
                    contact_info[field] = ""

            transformed_influencer["contact"] = contact_info

        transformed_influencers.append(transformed_influencer)

    # Create the final result structure
    transformed_result = {
        "metadata": influencer_data.get("metadata", {}),
        "influencers": transformed_influencers
    }

    logger.info(f"Transformation complete, returning {len(transformed_influencers)} influencers")
    return transformed_result

@app.get("/api/get-task-result/{task_id}")
async def get_task_result(task_id: str):
    # Log the request for debugging
    logger.info(f"Getting result for task {task_id}")
    logger.info(f"Current tasks: {list(tasks.keys())}")

    # Try loading tasks from file if task_id not found
    if task_id not in tasks:
        logger.warning(f"Task ID {task_id} not found in memory, trying to load from file")
        try:
            if os.path.exists(tasks_file):
                with open(tasks_file, 'r') as f:
                    loaded_tasks = json.load(f)
                    if task_id in loaded_tasks:
                        # Add this task back to our in-memory tasks
                        tasks[task_id] = loaded_tasks[task_id]
                        logger.info(f"Loaded task {task_id} from file")
        except Exception as e:
            logger.error(f"Error loading tasks from file: {e}")

    if task_id in tasks and tasks[task_id]["status"] == "completed":
        try:
            # Check if result is directly in the task or in a file
            if "result" in tasks[task_id]:
                result_data = tasks[task_id]["result"]

                # If result is a string, it might be a file path or JSON string
                if isinstance(result_data, str):
                    # First check if it's a file path
                    if os.path.exists(result_data):
                        logger.info(f"Loading result from file: {result_data}")
                        try:
                            with open(result_data, "r") as f:
                                result = json.load(f)
                        except Exception as file_error:
                            logger.error(f"Error reading result file {result_data}: {file_error}")
                            return JSONResponse(content={"error": f"Error reading result file: {str(file_error)}"}, status_code=500)
                    else:
                        # Try to parse it as a JSON string
                        try:
                            result = json.loads(result_data)
                            logger.info(f"Parsed result from JSON string")
                        except json.JSONDecodeError:
                            # If it's not a valid JSON string, check if there's a result_file in the task
                            result_file = tasks[task_id].get("result_file")
                            if result_file and os.path.exists(result_file):
                                logger.info(f"Loading result from result_file: {result_file}")
                                try:
                                    with open(result_file, "r") as f:
                                        result = json.load(f)
                                except Exception as file_error:
                                    logger.error(f"Error reading result_file {result_file}: {file_error}")
                                    return JSONResponse(content={"error": f"Error reading result_file: {str(file_error)}"}, status_code=500)
                            else:
                                # If all else fails, return a default result with the task metadata
                                logger.error(f"Result is not a valid JSON string and no result_file found")
                                # Create a default result with metadata
                                result = {
                                    "metadata": tasks[task_id].get("metadata", {}),
                                    "influencers": []
                                }
                # If result is already a dictionary, use it directly
                elif isinstance(result_data, dict):
                    logger.info("Result is already a dictionary, using directly")
                    result = result_data
                # If result is None or another type, check if there's a result_file
                else:
                    result_file = tasks[task_id].get("result_file")
                    if result_file and os.path.exists(result_file):
                        logger.info(f"Loading result from result_file: {result_file}")
                        try:
                            with open(result_file, "r") as f:
                                result = json.load(f)
                        except Exception as file_error:
                            logger.error(f"Error reading result_file {result_file}: {file_error}")
                            return JSONResponse(content={"error": f"Error reading result_file: {str(file_error)}"}, status_code=500)
                    else:
                        logger.error(f"Result has unexpected type: {type(result_data)} and no result_file found")
                        return JSONResponse(content={"error": f"Result has unexpected type: {type(result_data)}"}, status_code=500)

                # Transform the result to match the desired format
                try:
                    transformed_result = transform_influencer_data(result)
                    return JSONResponse(content={"task_id": task_id, "result": transformed_result})
                except Exception as transform_error:
                    logger.error(f"Error transforming result: {transform_error}")
                    # Return the original result if transformation fails
                    return JSONResponse(content={"task_id": task_id, "result": result})
            else:
                logger.error(f"Task {task_id} is marked as completed but has no result")
                return JSONResponse(content={"error": "Task is completed but has no result"}, status_code=500)
        except Exception as e:
            logger.error(f"Error getting result for task {task_id}: {e}")
            return JSONResponse(content={"error": f"Error retrieving result: {str(e)}"}, status_code=500)
    elif task_id in tasks:
        status = tasks[task_id]["status"]
        if status == "failed":
            error = tasks[task_id].get("error", "Unknown error")
            return JSONResponse(content={"error": f"Task failed: {error}"}, status_code=500)
        return JSONResponse(content={"status": status, "message": "Task not completed yet"}, status_code=202)
    else:
        logger.warning(f"Task ID {task_id} not found for result retrieval")
        return JSONResponse(content={"error": "Task ID not found"}, status_code=404)

# Add an endpoint to list all tasks
@app.get("/api/list-tasks")
async def list_tasks():
    task_list = [{"task_id": tid, "status": info["status"]} for tid, info in tasks.items()]
    return JSONResponse(content={"tasks": task_list})

# Add cleanup endpoint to remove completed tasks older than a certain time
@app.post("/api/cleanup-tasks")
async def cleanup_tasks():
    # In a real implementation, you would check timestamps
    # For now, we'll just keep the tasks dictionary as is
    return JSONResponse(content={"message": "Task cleanup feature not implemented yet"})

# Add signal handlers to ensure tasks are saved on exit
def handle_exit(signum, _):  # Using _ to indicate unused parameter
    """Handle exit signals by saving tasks"""
    logger.warning(f"Received signal {signum}, saving tasks before exit")
    force_save_tasks()
    logger.info("Tasks saved successfully before exit")

# Register signal handlers
signal.signal(signal.SIGINT, handle_exit)
signal.signal(signal.SIGTERM, handle_exit)

@app.on_event("startup")
async def startup_event():
    """Initialize on server startup"""
    logger.info("Server starting up")
    # Make sure results directory exists
    os.makedirs(results_dir, exist_ok=True)
    # Load tasks from disk
    try:
        if os.path.exists(tasks_file):
            with open(tasks_file, 'r') as f:
                loaded_tasks = json.load(f)
                # Update global tasks dictionary
                tasks.update(loaded_tasks)
                logger.info(f"Loaded {len(loaded_tasks)} tasks from {tasks_file}")
    except Exception as e:
        logger.error(f"Error loading tasks on startup: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on server shutdown"""
    logger.info("Server shutting down, saving tasks")
    force_save_tasks()
    logger.info("Tasks saved successfully on shutdown")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8350)
    print("Server running on http://localhost:8350")