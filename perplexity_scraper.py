import asyncio
import json
import os
import random
import time
import logging
import re
from typing import Dict, List, Any

# Set up logging
try:
    from loguru import logger
except ImportError:
    # Fallback to standard logging if loguru is not available
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

# Try to load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logger.warning("dotenv not available, using environment variables directly")

# Import the global rate limiter
try:
    # First try to import from our dedicated module
    from global_rate_limiter import global_rate_limiter
    GLOBAL_RATE_LIMITER_AVAILABLE = True
except ImportError:
    # Fall back to trying to import from social_media_collection
    try:
        from social_media_collection import global_rate_limiter
        GLOBAL_RATE_LIMITER_AVAILABLE = True
    except ImportError:
        GLOBAL_RATE_LIMITER_AVAILABLE = False
        logger.warning("Could not import global rate limiter, using only local rate limiting")

# Get Perplexity API key
# The API key from environment variable or use the hardcoded one
PERPLEXITY_API_KEY = os.getenv('PERPLEXITY_AI_API', '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')

# If the API key is not working, you may need to update it with a new one
# For testing purposes, we'll add a fallback mechanism to handle API key issues
def get_perplexity_api_key():
    """Get a valid Perplexity API key, with fallback options."""
    global PERPLEXITY_API_KEY

    # Try the current API key first
    if PERPLEXITY_API_KEY:
        return PERPLEXITY_API_KEY

    # If no API key is available, return a default one (this is just for testing)
    return '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'

# Define perplexity_data function directly
def perplexity_data(prompt_or_payload, system_prompt="Be precise and concise.", options=False, api_key=None, max_retries=3):
    """
    Call the Perplexity API to get a response with retry logic for rate limits.

    Args:
        prompt_or_payload: Either a string prompt or a complete payload dictionary
        system_prompt: System prompt to use if prompt_or_payload is a string
        options: Whether to include web search options
        api_key: Perplexity API key
        max_retries: Maximum number of retries for rate limit errors

    Returns:
        str: Response from Perplexity
    """
    import requests
    import time

    # Get the API key if not provided
    if api_key is None:
        api_key = get_perplexity_api_key()

    url = "https://api.perplexity.ai/chat/completions"

    # Check if prompt_or_payload is already a complete payload
    if isinstance(prompt_or_payload, dict) and "messages" in prompt_or_payload:
        payload = prompt_or_payload
    else:
        # Build payload from prompt
        payload = {
            "model": "sonar-reasoning",
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": prompt_or_payload
                }
            ],
            "temperature": 0.2,
            "top_p": 0.9,
            "return_citations": True,
            "return_images": False,
            "return_related_questions": False,
            "search_recency_filter": "month",
            "top_k": 0,
            "stream": False,
            "presence_penalty": 0,
            "frequency_penalty": 1
        }

    if options:
        # Add web search options
        payload.update({
            "web_search_options": {
                "search_context_size": "high"
            }
        })

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # Retry logic for rate limit errors
    for attempt in range(max_retries + 1):
        try:
            # Use synchronous request with SSL verification disabled if needed
            try:
                import ssl
                from urllib3.util import ssl_
                requests.packages.urllib3.disable_warnings()
                response = requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    verify=False,  # Disable SSL verification
                    timeout=None  # No timeout for Perplexity API calls to allow complete data retrieval
                )
            except (ImportError, AttributeError):
                # Fall back to regular request if urllib3 isn't available or configured properly
                response = requests.post(url, json=payload, headers=headers, timeout=None)

            # Check if the request was successful
            if response.status_code == 200:
                response_data = response.json()
                try:
                    # Extract the message content
                    message_content = response_data['choices'][0]['message']['content']

                    # Filter out thinking tags
                    message_content = re.sub(r'<think>.*?</think>', '', message_content)
                    message_content = re.sub(r'<end_think>.*?</end_think>', '', message_content)

                    return message_content
                except (KeyError, IndexError):
                    logger.error("Unexpected response format from Perplexity API")
                    return "{}"

            # Handle rate limit errors (429)
            elif response.status_code == 429:
                if attempt < max_retries:
                    wait_time = (2 ** attempt) + random.uniform(1, 3)  # Exponential backoff
                    logger.warning(f"Rate limit hit, retrying in {wait_time:.2f} seconds (attempt {attempt + 1}/{max_retries + 1})")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error("Rate limit exceeded, max retries reached")
                    return "{}"

            # Handle other errors
            else:
                logger.error(f"Request to Perplexity API failed with status code: {response.status_code}")

                # If the API key is invalid (401), return a mock response for testing
                if response.status_code == 401:
                    logger.warning("API key may be invalid or expired, using fallback data")
                    return "{}"

                return "{}"

        except requests.exceptions.Timeout:
            logger.error(f"Timeout calling Perplexity API (this should not happen with timeout=None)")
            if attempt < max_retries:
                wait_time = (2 ** attempt) + random.uniform(1, 3)
                logger.warning(f"Retrying after unexpected timeout in {wait_time:.2f} seconds (attempt {attempt + 1}/{max_retries + 1})")
                time.sleep(wait_time)
                continue
            else:
                logger.error("Max retries reached after timeout")
                return "{}"
        except Exception as e:
            logger.error(f"Error calling Perplexity API: {e}")
            if attempt < max_retries:
                wait_time = (2 ** attempt) + random.uniform(1, 3)
                logger.warning(f"Retrying in {wait_time:.2f} seconds (attempt {attempt + 1}/{max_retries + 1})")
                time.sleep(wait_time)
                continue
            else:
                return "{}"

    return "{}"

# Define async version for better performance
async def perplexity_data_async(prompt_or_payload, system_prompt="Be precise and concise.", options=False, api_key=None, max_retries=3):
    """
    Async version of perplexity_data function to avoid blocking the event loop.
    """
    import asyncio
    import concurrent.futures

    # Run the synchronous function in a thread pool to avoid blocking
    loop = asyncio.get_event_loop()
    with concurrent.futures.ThreadPoolExecutor() as executor:
        result = await loop.run_in_executor(
            executor,
            perplexity_data,
            prompt_or_payload,
            system_prompt,
            options,
            api_key,
            max_retries
        )
    return result

# Keep the old async function for compatibility
async def perplexity_api_with_rate_limit(payload, api_key=None):
    """
    Async version of perplexity_data function.
    Not used in this implementation but included for compatibility.
    """
    # Get the API key if not provided
    if api_key is None:
        api_key = get_perplexity_api_key()

    # Use the new async version
    return await perplexity_data_async(payload, api_key=api_key)

# Rate limiter class for individual platforms
class RateLimiter:
    def __init__(self, calls_per_minute=10):
        self.calls_per_minute = calls_per_minute
        self.call_timestamps = []
        self.lock = asyncio.Lock()

    async def wait(self):
        async with self.lock:
            now = time.time()
            # Remove timestamps older than 1 minute
            self.call_timestamps = [ts for ts in self.call_timestamps if now - ts < 60]

            # If we've reached the limit, wait until we can make another call
            if len(self.call_timestamps) >= self.calls_per_minute:
                wait_time = 60 - (now - self.call_timestamps[0]) + random.uniform(0.1, 1.0)
                logger.info(f"Rate limit reached, waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
                # After waiting, remove timestamps older than 1 minute again
                now = time.time()
                self.call_timestamps = [ts for ts in self.call_timestamps if now - ts < 60]

            # Add current timestamp
            self.call_timestamps.append(now)

# Create rate limiters for each platform (increased for Pro version)
instagram_rate_limiter = RateLimiter(calls_per_minute=30)
youtube_rate_limiter = RateLimiter(calls_per_minute=30)
facebook_rate_limiter = RateLimiter(calls_per_minute=20)
tiktok_rate_limiter = RateLimiter(calls_per_minute=30)

# Validation functions removed - direct Perplexity queries only


# All validation functions removed - direct Perplexity queries only


def extract_json_from_text(text: str, default_value: dict = None) -> dict:
    """Extract JSON from text response."""
    if not default_value:
        default_value = {}

    if not text:
        return default_value

    try:
        # Remove thinking tags that Perplexity sometimes includes
        text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
        text = text.strip()

        # Try to find JSON in the text with markdown code blocks
        json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', text)
        if json_match:
            json_str = json_match.group(1)
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                # Try to clean up the JSON string
                json_str = re.sub(r'//.*?$', '', json_str, flags=re.MULTILINE)  # Remove comments
                json_str = re.sub(r',\s*}', '}', json_str)  # Remove trailing commas
                json_str = re.sub(r',\s*]', ']', json_str)  # Remove trailing commas in arrays
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    pass

        # Try to find JSON without markdown code blocks - look for complete JSON objects
        json_match = re.search(r'({[^{}]*(?:{[^{}]*}[^{}]*)*})', text)
        if json_match:
            json_str = json_match.group(1)
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                # Try to clean up the JSON string
                json_str = re.sub(r'//.*?$', '', json_str, flags=re.MULTILINE)  # Remove comments
                json_str = re.sub(r',\s*}', '}', json_str)  # Remove trailing commas
                json_str = re.sub(r',\s*]', ']', json_str)  # Remove trailing commas in arrays
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    pass

        # If the entire text is valid JSON, parse it
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # Try to extract a JSON object with more flexible matching
        json_match = re.search(r'({[\s\S]*?})', text)
        if json_match:
            json_str = json_match.group(1)
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                pass

        return default_value
    except (AttributeError, TypeError) as e:
        logger.error(f"Error extracting JSON from text: {e}")
        return default_value

# Fallback extraction functions removed - direct Perplexity queries only

# All fallback extraction functions removed - direct Perplexity queries only

async def get_instagram_details(username: str) -> dict:
    """
    Get Instagram details using the Perplexity API.

    Args:
        username (str): Instagram username

    Returns:
        dict: Instagram details
    """
    if not username:
        logger.warning("Empty Instagram username provided")
        return {
            "error": True,
            "username": username,
            "source": "instagram",
            "followers_count": "0",
            "Instagram_followers": "0",
            "name": username.capitalize() if username else "",
            "image": "",
            "bio": "",
            "is_verified": False,
            "profile_image": ""
        }

    # Clean the username - remove @ and other special characters
    username = username.strip().lstrip('@')
    if not username:
        logger.warning("Username became empty after cleaning")
        return {
            "error": True,
            "username": username,
            "source": "instagram",
            "followers_count": "0",
            "Instagram_followers": "0",
            "name": "",
            "image": "",
            "bio": "",
            "is_verified": False,
            "profile_image": ""
        }

    logger.info(f"Fetching Instagram details for {username} using Perplexity API")

    # Rate limiting removed - will only retry on actual API rate limit errors

    try:
        # Create a direct prompt for Perplexity to get exact Instagram data
        prompt = f"""
        Find the exact current Instagram data for @{username}. Search for their Instagram profile and get the real numbers.

        Search for:
        1. "@{username} Instagram" to find their profile
        2. "{username} Instagram followers" to find current follower count
        3. Check instagram.com/{username} directly
        4. Look for recent social media analytics or news about their follower count

        Return ONLY this JSON with the exact data you find:
        {{
            "username": "{username}",
            "full_name": "Exact display name from Instagram profile",
            "follower_count": "Exact follower count with K/M/B (e.g. 1.2M, 850K, 45000)",
            "bio": "Complete bio text from their Instagram",
            "verified": true/false,
            "profile_image_url": "Direct URL to profile image"
        }}

        Get the EXACT follower count - not estimates or approximations. If you can't find exact data, return "0" for that field.
        """

        # Call the Perplexity API (async version to avoid blocking)
        response = await perplexity_data_async(prompt, api_key=PERPLEXITY_API_KEY, options=True)

        # Extract JSON from the response
        data = extract_json_from_text(response)

        # Format the data to match our expected structure
        if data and isinstance(data, dict):
            # Map the fields from the response to our expected structure
            formatted_data = {
                "error": False,
                "username": username,
                "source": "instagram",
                "followers_count": data.get("follower_count", "0"),
                "Instagram_followers": data.get("follower_count", "0"),
                "name": data.get("full_name", username.capitalize()),
                "bio": data.get("bio", ""),
                "is_verified": data.get("verified", False),
                "profile_image": data.get("profile_image_url", ""),
                "image": data.get("profile_image_url", ""),
                "verification_confidence": data.get("verification_confidence", "unknown"),
                "data_source": data.get("data_source", "perplexity")
            }

            # NO alternative field checking - use direct Perplexity results only

            # Set error flag based on followers count
            formatted_data["error"] = formatted_data["followers_count"] == "0"

            logger.info(f"Successfully retrieved Instagram details for {username}")
            return formatted_data
        else:
            logger.error(f"Failed to extract JSON from Perplexity response for {username}")
            # Return what we have from Perplexity directly
            return {
                "error": True,
                "username": username,
                "source": "instagram",
                "followers_count": "0",
                "Instagram_followers": "0",
                "name": username.capitalize(),
                "image": "",
                "bio": "",
                "is_verified": False,
                "profile_image": ""
            }

    except Exception as e:
        logger.error(f"Error fetching Instagram details for {username}: {e}")
        return {
            "error": True,
            "username": username,
            "source": "instagram",
            "followers_count": "0",
            "Instagram_followers": "0",
            "name": username.capitalize(),
            "image": "",
            "bio": "",
            "is_verified": False,
            "profile_image": ""
        }

async def fetch_youtube_feed(channel_name: str) -> dict:
    """
    Get YouTube channel details using ONLY direct Perplexity API queries.
    No fallbacks, no hardcoded data, no pattern matching.

    Args:
        channel_name (str): YouTube channel name, handle, or ID

    Returns:
        dict: YouTube channel details from Perplexity only
    """
    if not channel_name:
        logger.warning("Empty YouTube channel name provided")
        return {
            "error": True,
            "username": "",
            "source": "youtube",
            "followers_count": "0",
            "subscribers": "0",
            "Youtube_subscribers": "0",
            "total_views": "0",
            "total_videos": "0",
            "name": "",
            "title": "",
            "link": "",
            "id": "",
            "description": ""
        }

    # Clean the channel name
    channel_name = channel_name.strip().lstrip('@')
    if not channel_name:
        logger.warning("Channel name became empty after cleaning")
        return {
            "error": True,
            "username": "",
            "source": "youtube",
            "followers_count": "0",
            "subscribers": "0",
            "Youtube_subscribers": "0",
            "total_views": "0",
            "total_videos": "0",
            "name": "",
            "title": "",
            "link": "",
            "id": "",
            "description": ""
        }

    logger.info(f"Fetching YouTube details for {channel_name} using DIRECT Perplexity query only")

    try:
        # NATURAL Perplexity query that gets complete data with exact numbers
        prompt = f"""
        I need complete information about {channel_name}'s YouTube channel. Please search for and find all current details including:

        - Channel name and URL
        - Current subscriber count (exact number, not approximations)
        - Total channel views (exact number, not approximations)
        - Number of videos published (exact count)
        - Channel description/about section
        - Channel ID

        Please search thoroughly for EXACT numerical values, then format your response as this JSON:
        {{
            "channel_name": "actual channel name",
            "channel_url": "actual YouTube URL",
            "channel_id": "actual channel ID",
            "subscriber_count": "exact subscriber count (no + signs or approximations)",
            "total_view_count": "exact total views (no + signs or approximations)",
            "number_of_videos": "exact video count (no + signs or approximations)",
            "channel_description": "actual channel description"
        }}

        IMPORTANT:
        - Find EXACT numbers, not approximations like "750k+" or "120M+"
        - Use precise values like "750000" instead of "750k+"
        - Use precise values like "120000000" instead of "120M+"
        - If exact numbers aren't available, use the most precise number you can find
        """

        # Call Perplexity API directly
        response = await perplexity_data_async(prompt, api_key=PERPLEXITY_API_KEY, options=True)

        # Extract JSON from response
        data = extract_json_from_text(response)

        if data and isinstance(data, dict):
            logger.info(f"Raw YouTube data from Perplexity for {channel_name}: {data}")

            # Improved data cleaning - preserve actual data, only clean obvious placeholders
            def clean_value(value):
                if value is None:
                    return ""

                value_str = str(value).strip()

                # List of placeholder values that should be cleaned to empty string
                placeholder_values = [
                    "", "0", "N/A", "Not available", "null", "None",
                    "Data unavailable", "Data not available",
                    "Not found in search results", "Not found",
                    "URL not available in provided sources",
                    "Channel ID not available in provided sources",
                    "Channel description not available in provided sources",
                    "Not available in provided sources"
                ]

                if value_str in placeholder_values:
                    return ""

                return value_str

            # Videos list removed - no longer needed

            result = {
                "error": False,
                "username": channel_name,
                "source": "youtube",
                "title": clean_value(data.get("channel_name", "")),
                "name": clean_value(data.get("channel_name", "")),
                "link": clean_value(data.get("channel_url", "")),
                "id": clean_value(data.get("channel_id", "")),
                "description": clean_value(data.get("channel_description", "")),
                "Youtube_subscribers": clean_value(data.get("subscriber_count", "")),
                "followers_count": clean_value(data.get("subscriber_count", "")),
                "subscribers": clean_value(data.get("subscriber_count", "")),
                "total_views": clean_value(data.get("total_view_count", "")),
                "total_videos": clean_value(data.get("number_of_videos", ""))
            }

            # Log comprehensive results
            logger.info(f"YouTube data for {channel_name}: subscribers={result['subscribers']}, views={result['total_views']}, videos={result['total_videos']}, channel_id={result['id']}, description_length={len(result['description'])}")
            return result
        else:
            logger.error(f"No valid JSON data from Perplexity for {channel_name}")
            return {
                "error": True,
                "username": channel_name,
                "source": "youtube",
                "followers_count": "0",
                "subscribers": "0",
                "Youtube_subscribers": "0",
                "total_views": "0",
                "total_videos": "0",
                "name": "",
                "title": "",
                "link": "",
                "id": "",
                "description": ""
            }

    except Exception as e:
        logger.error(f"Error fetching YouTube details for {channel_name}: {e}")
        return {
            "error": True,
            "username": channel_name,
            "source": "youtube",
            "followers_count": "0",
            "subscribers": "0",
            "Youtube_subscribers": "0",
            "total_views": "0",
            "total_videos": "0",
            "name": "",
            "title": "",
            "link": "",
            "id": "",
            "description": ""
        }

async def get_facebook_details(username: str) -> dict:
    """
    Get Facebook page/profile details using ONLY direct Perplexity API queries.
    No fallbacks, no hardcoded data, no pattern matching.

    Args:
        username (str): Facebook username or page name

    Returns:
        dict: Facebook page/profile details from Perplexity only
    """
    if not username:
        logger.warning("Empty Facebook username provided")
        return {
            "error": True,
            "username": "",
            "source": "facebook",
            "followers_count": "0",
            "Facebook_followers": "0",
            "name": "",
            "url": "",
            "title": "",
            "image": ""
        }

    # Clean the username
    username = username.strip().lstrip('@')
    if not username:
        logger.warning("Username became empty after cleaning")
        return {
            "error": True,
            "username": "",
            "source": "facebook",
            "followers_count": "0",
            "Facebook_followers": "0",
            "name": "",
            "url": "",
            "title": "",
            "image": ""
        }

    logger.info(f"Fetching Facebook details for {username} using DIRECT Perplexity query only")

    try:
        # DIRECT Perplexity query - no fallbacks, no pattern matching
        prompt = f"""
        Find the official Facebook page for "{username}". Search for their verified Facebook presence.

        Search for:
        1. "{username} official Facebook page"
        2. "{username} Facebook verified"
        3. "{username} Facebook page followers"
        4. Look up facebook.com/{username}
        5. Check for their business or public figure page
        6. Search for recent Facebook posts or activity

        If you find their actual Facebook page, return this JSON with the real data:
        {{
            "name": "Real page name from Facebook",
            "username": "Real Facebook username/handle",
            "url": "Real Facebook page URL",
            "follower_count": "Real follower count (e.g. 15.2M, 850K, 45000)",
            "likes": "Page likes if different from followers",
            "about": "Page description/bio",
            "profile_image_url": "Direct link to profile image"
        }}

        IMPORTANT:
        - Only return actual data from real Facebook pages
        - If the person has no Facebook page, return empty strings "" for all fields
        - Do not return "0" unless that's the actual follower count
        - Do not make up or estimate data
        """

        # Call Perplexity API directly
        response = await perplexity_data_async(prompt, api_key=PERPLEXITY_API_KEY, options=True)

        # Extract JSON from response
        data = extract_json_from_text(response)

        if data and isinstance(data, dict):
            logger.info(f"Raw Facebook data from Perplexity for {username}: {data}")

            # Map directly from Perplexity response - no modifications
            result = {
                "error": False,
                "username": username,
                "source": "facebook",
                "url": data.get("url", ""),
                "title": data.get("name", ""),
                "name": data.get("name", ""),
                "Facebook_followers": data.get("follower_count", "0"),
                "followers_count": data.get("follower_count", "0"),
                "image": data.get("profile_image_url", "")
            }

            logger.info(f"Facebook data for {username}: followers={result['followers_count']}, name={result['name']}")
            return result
        else:
            logger.error(f"No valid JSON data from Perplexity for {username}")
            return {
                "error": True,
                "username": username,
                "source": "facebook",
                "followers_count": "0",
                "Facebook_followers": "0",
                "name": "",
                "url": "",
                "title": "",
                "image": ""
            }

    except Exception as e:
        logger.error(f"Error fetching Facebook details for {username}: {e}")
        return {
            "error": True,
            "username": username,
            "source": "facebook",
            "followers_count": "0",
            "Facebook_followers": "0",
            "name": "",
            "url": "",
            "title": "",
            "image": ""
        }

async def get_facebook_details_batch(usernames: List[str]) -> Dict[str, dict]:
    """
    Get Facebook details for multiple usernames in batch.

    Args:
        usernames (List[str]): List of Facebook usernames

    Returns:
        Dict[str, dict]: Dictionary mapping usernames to their details
    """
    results = {}

    for username in usernames:
        results[username] = await get_facebook_details(username)

    return results

# Initialize functions (can be called to set up any required state)
async def init_instagram():
    """Initialize Instagram scraper."""
    logger.info("Initializing Instagram scraper with Perplexity")
    pass

async def init_facebook():
    """Initialize Facebook scraper."""
    logger.info("Initializing Facebook scraper with Perplexity")
    pass

async def init_youtube():
    """Initialize YouTube scraper."""
    logger.info("Initializing YouTube scraper with Perplexity")
    pass

async def get_contact_details(influencer_name: str) -> dict:
    """
    Get contact details for an influencer using ONLY direct Perplexity API queries.
    No fallbacks, no hardcoded data, no validation logic.

    Args:
        influencer_name (str): The name of the influencer

    Returns:
        dict: Contact details from Perplexity only
    """
    if not influencer_name:
        logger.warning("Empty influencer name provided")
        return {
            "error": True,
            "name": "",
            "email": "",
            "phone": "",
            "website": "",
            "address": "",
            "management_company": "",
            "manager_name": "",
            "manager_email": "",
            "manager_phone": "",
            "profile_image": ""
        }

    logger.info(f"Fetching contact details for {influencer_name} using DIRECT Perplexity query only")

    try:
        # DIRECT Perplexity query - no fallbacks, no validation
        prompt = f"""
        Search for publicly available contact information for "{influencer_name}". This person may be a content creator, influencer, or public figure.

        Search comprehensively:
        1. "{influencer_name} contact information"
        2. "{influencer_name} official website"
        3. "{influencer_name} business email"
        4. "{influencer_name} management company"
        5. "{influencer_name} agent contact"
        6. Check their social media bios for contact links
        7. Look for official press releases or media kits

        Find their ACTUAL contact information and return ONLY this JSON with REAL data:
        {{
            "name": "Real full name",
            "email": "Real business email if publicly available",
            "phone": "Real business phone if publicly available",
            "website": "Real official website URL",
            "address": "Real business address if publicly available",
            "management_company": "Real management company name",
            "manager_name": "Real manager name if available",
            "manager_email": "Real manager email if available",
            "manager_phone": "Real manager phone if available",
            "profile_image": "Direct URL to official profile image"
        }}

        CRITICAL:
        - Only return actual publicly available contact information
        - If no contact information exists, return empty strings "" for all fields
        - Do not fabricate or guess any contact details
        - Only include information that is meant for business/professional contact
        """

        # Call Perplexity API directly
        response = await perplexity_data_async(prompt, api_key=PERPLEXITY_API_KEY, options=True)

        # Extract JSON from response
        data = extract_json_from_text(response)

        if data and isinstance(data, dict):
            logger.info(f"Raw contact data from Perplexity for {influencer_name}: {data}")

            # Map directly from Perplexity response - no modifications
            result = {
                "error": False,
                "name": data.get("name", ""),
                "email": data.get("email", ""),
                "phone": data.get("phone", ""),
                "website": data.get("website", ""),
                "address": data.get("address", ""),
                "management_company": data.get("management_company", ""),
                "manager_name": data.get("manager_name", ""),
                "manager_email": data.get("manager_email", ""),
                "manager_phone": data.get("manager_phone", ""),
                "profile_image": data.get("profile_image", "")
            }

            logger.info(f"Contact data for {influencer_name}: website={result['website']}, email={result['email']}, management={result['management_company']}")
            return result
        else:
            logger.error(f"No valid JSON data from Perplexity for {influencer_name}")
            return {
                "error": True,
                "name": influencer_name,
                "email": "",
                "phone": "",
                "website": "",
                "address": "",
                "management_company": "",
                "manager_name": "",
                "manager_email": "",
                "manager_phone": "",
                "profile_image": ""
            }

    except Exception as e:
        logger.error(f"Error fetching contact details for {influencer_name}: {e}")
        return {
            "error": True,
            "name": influencer_name,
            "email": "",
            "phone": "",
            "website": "",
            "address": "",
            "management_company": "",
            "manager_name": "",
            "manager_email": "",
            "manager_phone": "",
            "profile_image": ""
        }

async def get_tiktok_details(username: str) -> dict:
    """
    Get TikTok account details using ONLY direct Perplexity API queries.
    No fallbacks, no hardcoded data, no pattern matching.

    Args:
        username (str): TikTok username or handle

    Returns:
        dict: TikTok account details from Perplexity only
    """
    if not username:
        logger.warning("Empty TikTok username provided")
        return {
            "error": True,
            "username": "",
            "source": "tiktok",
            "followers_count": "0",
            "Tiktok_followers": "0",
            "name": "",
            "bio": "",
            "link": ""
        }

    # Clean the username
    username = username.strip().lstrip('@')
    if not username:
        logger.warning("Username became empty after cleaning")
        return {
            "error": True,
            "username": "",
            "source": "tiktok",
            "followers_count": "0",
            "Tiktok_followers": "0",
            "name": "",
            "bio": "",
            "link": ""
        }

    logger.info(f"Fetching TikTok details for {username} using DIRECT Perplexity query only")

    try:
        # DIRECT Perplexity query - no fallbacks, no pattern matching
        prompt = f"""
        Search for the TikTok account of "{username}". This person may be a content creator, influencer, or public figure.

        Search comprehensively:
        1. "@{username} TikTok"
        2. "{username} TikTok account"
        3. "{username} TikTok followers"
        4. Check tiktok.com/@{username}
        5. Look for their official TikTok presence
        6. Check if they have alternative names or handles

        Find their ACTUAL TikTok account and return ONLY this JSON with REAL data:
        {{
            "username": "Actual TikTok username",
            "full_name": "Actual display name from TikTok",
            "follower_count": "Current follower count with K/M/B (e.g. 15.2M, 850K, 45000)",
            "bio": "Actual bio text from TikTok",
            "verified": true/false,
            "profile_image_url": "Direct link to profile image",
            "link": "Actual TikTok profile URL (e.g. https://tiktok.com/@username)",
            "total_likes": "Total likes if available",
            "video_count": "Number of videos if available"
        }}

        CRITICAL: Only return real data from actual TikTok accounts. If no TikTok account exists for this person, return empty strings "" for all fields.
        """

        # Call Perplexity API directly
        response = await perplexity_data_async(prompt, api_key=PERPLEXITY_API_KEY, options=True)

        # Extract JSON from response
        data = extract_json_from_text(response)

        if data and isinstance(data, dict):
            logger.info(f"Raw TikTok data from Perplexity for {username}: {data}")

            # Map directly from Perplexity response - no modifications
            result = {
                "error": False,
                "username": username,
                "source": "tiktok",
                "name": data.get("full_name", ""),
                "Tiktok_followers": data.get("follower_count", "0"),
                "followers_count": data.get("follower_count", "0"),
                "bio": data.get("bio", ""),
                "link": data.get("link", "")
            }

            logger.info(f"TikTok data for {username}: followers={result['followers_count']}, name={result['name']}")
            return result
        else:
            logger.error(f"No valid JSON data from Perplexity for {username}")
            return {
                "error": True,
                "username": username,
                "source": "tiktok",
                "followers_count": "0",
                "Tiktok_followers": "0",
                "name": "",
                "bio": "",
                "link": ""
            }

    except Exception as e:
        logger.error(f"Error fetching TikTok details for {username}: {e}")
        return {
            "error": True,
            "username": username,
            "source": "tiktok",
            "followers_count": "0",
            "Tiktok_followers": "0",
            "name": "",
            "bio": "",
            "link": ""
        }

# fetch_missing_social_media_data function removed - direct Perplexity queries only

async def removed_fetch_missing_social_media_data(influencer_name: str, platform: str, username: str = None) -> dict:
    """
    Fetch missing social media data when initial scraping returns null values.
    Uses a more aggressive approach with direct prompts about the influencer.

    Args:
        influencer_name (str): The name of the influencer
        platform (str): The social media platform (instagram, youtube, facebook, tiktok)
        username (str, optional): The username to use for the query. If None, will use influencer_name

    Returns:
        dict: Social media details with any missing data filled in
    """
    if not username:
        logger.warning(f"No username provided for {influencer_name}, cannot fetch missing data")
        return fallback_data

    # Default fallback data structure
    fallback_data = {
        "error": True,
        "username": username,
        "name": influencer_name,
        "source": platform.lower(),
        "followers_count": "0"
    }

    # Add platform-specific fields without generating fake URLs
    if platform.lower() == "instagram":
        fallback_data["Instagram_followers"] = "0"
        fallback_data["image"] = ""
    elif platform.lower() == "youtube":
        fallback_data["subscribers"] = "0"
        fallback_data["Youtube_subscribers"] = "0"
        fallback_data["total_views"] = "0"
        fallback_data["total_videos"] = "0"
        fallback_data["videos"] = []
        fallback_data["title"] = influencer_name
        fallback_data["link"] = ""
    elif platform.lower() == "facebook":
        fallback_data["Facebook_followers"] = "0"
        fallback_data["url"] = ""  # Don't generate fake Facebook URLs
        fallback_data["image"] = ""
    elif platform.lower() == "tiktok":
        fallback_data["Tiktok_followers"] = "0"
        fallback_data["link"] = ""  # Don't generate fake TikTok URLs
        fallback_data["image"] = ""

    logger.info(f"Fetching missing {platform} data for {influencer_name} (username: {username})")

    try:
        # Create a more direct prompt specifically for the influencer by name
        direct_prompt = None

        if platform.lower() == "instagram":
            direct_prompt = f"""
            I need VERIFIED and ACCURATE Instagram follower count for influencer "{influencer_name}" (username: @{username}).

            MANDATORY VERIFICATION STEPS:
            1. FIRST: Confirm @{username} is the correct Instagram account for "{influencer_name}"
            2. THEN: Get EXACT current follower count from verified sources:
               - Official Instagram profile (most recent count)
               - SocialBlade.com real-time data
               - Recent verified news articles (within 30 days)
               - Official media kits or press releases

            3. CROSS-VERIFICATION: Compare at least 2 sources
               - If sources differ by >10%, use most recent official source
               - If you cannot verify exact number, return "0"

            REQUIRED JSON FORMAT:
            {{
                "username": "exact_username_without_@",
                "name": "Full Display Name",
                "followers_count": "EXACT number with K/M/B suffix (e.g. 1.5M, 850K)",
                "profile_image_url": "https://direct.image.url/image.jpg",
                "verification_confidence": "high/medium/low"
            }}

            CRITICAL: NEVER return estimated or approximate numbers. Only return verified data or "0".
            """
        elif platform.lower() == "youtube":
            direct_prompt = f"""
            I need accurate subscriber count information for the YouTube channel of influencer "{influencer_name}" (username/channel: {username}).

            SEARCH INSTRUCTIONS:
            1. Search for "{influencer_name} youtube subscribers" and "{username} youtube subscribers"
            2. Check social media analytics sites, recent news articles, and brand partnership announcements
            3. Look for the most recent and reliable sources

            Return ONLY a valid JSON with these fields:
            - "username": The exact username/channel name
            - "name": The full display name
            - "subscribers": The EXACT subscriber count with K, M, or B suffix (e.g., "1.5M")
            - "total_views": Total view count if available
            - "total_videos": Approximate number of videos
            - "link": Full URL to the YouTube channel

            CRITICAL: NEVER return "N/A" or "0" for subscribers. If you can't find the exact number, provide your best estimate based on the influencer's prominence.
            """
        elif platform.lower() == "facebook":
            direct_prompt = f"""
            I need VERIFIED and ACCURATE Facebook follower count for influencer "{influencer_name}" (username: {username}).

            MANDATORY VERIFICATION STEPS:
            1. FIRST: Confirm facebook.com/{username} is the correct Facebook page for "{influencer_name}"
            2. THEN: Get EXACT current follower count from verified sources:
               - Official Facebook page (most recent count)
               - CrowdTangle or Facebook Analytics data
               - SocialBlade.com Facebook data
               - Recent verified news articles (within 30 days)
               - Official media kits or press releases

            3. CROSS-VERIFICATION: Compare at least 2 sources
               - If sources differ by >10%, use most recent official source
               - If you cannot verify exact number, return "0"

            REQUIRED JSON FORMAT:
            {{
                "username": "exact_username_without_@",
                "name": "Full Display Name",
                "followers_count": "EXACT number with K/M/B suffix (e.g. 1.5M, 850K)",
                "url": "https://www.facebook.com/exact_page_url",
                "verification_confidence": "high/medium/low"
            }}

            CRITICAL: NEVER return estimated or approximate numbers. Only return verified data or "0".
            """
        elif platform.lower() == "tiktok":
            direct_prompt = f"""
            I need the most accurate and comprehensive TikTok data for influencer "{influencer_name}" (username: @{username}). Search exhaustively across multiple sources.

            COMPREHENSIVE SEARCH STRATEGY:
            1. Official TikTok profile: tiktok.com/@{username}
            2. Social media analytics: SocialBlade, TikTok Analytics, Influencer Marketing Hub
            3. Recent news articles, press releases mentioning TikTok follower counts
            4. Cross-reference from Instagram, YouTube, or website TikTok links
            5. Brand partnership announcements or media kits
            6. TikTok Creator Fund announcements or official TikTok spotlights

            REQUIRED JSON FORMAT:
            {{
                "username": "exact_username_without_@",
                "name": "Full Display Name as shown on TikTok",
                "followers_count": "EXACT number with K/M/B suffix (e.g. 1.5M, 850K, 2.1B)",
                "link": "https://www.tiktok.com/@exact_username",
                "bio": "Complete bio/description text",
                "verified": true/false,
                "video_count": "EXACT number of videos posted",
                "likes_count": "Total likes across all videos with K/M/B suffix",
                "data_source": "specific source where you found this data",
                "last_updated": "Month Year when data was verified"
            }}

            ACCURACY REQUIREMENTS:
            - Follower count must be EXACT current number, not approximate
            - Video count must be exact number of published videos
            - Bio must be complete text, not summarized
            - Link must be the actual working TikTok profile URL
            - NEVER use "Approximately", "Around", "About", or similar vague terms
            - If you cannot find exact follower count, return "0" for followers_count field
            - If you cannot find other data, return empty string "" for that field
            - Always provide your best estimate based on available information

            VERIFICATION: Cross-check follower count across multiple analytics sources for accuracy.

            Return ONLY the JSON object. No explanatory text.
            """

        # If we have a direct prompt, use it
        if direct_prompt:
            logger.info(f"Using direct prompt for {platform} data for {influencer_name}")
            response = await perplexity_data_async(direct_prompt, api_key=PERPLEXITY_API_KEY, options=True)
            data = extract_json_from_text(response)

            if data and isinstance(data, dict):
                # Format the data based on the platform
                if platform.lower() == "instagram":
                    result = {
                        "error": False,
                        "username": username,
                        "source": "instagram",
                        "name": data.get("name", influencer_name),
                        "followers_count": data.get("followers_count", "0"),
                        "Instagram_followers": data.get("followers_count", "0"),
                        "profile_image": data.get("profile_image_url", ""),
                        "image": data.get("profile_image_url", "")
                    }
                elif platform.lower() == "youtube":
                    result = {
                        "error": False,
                        "username": username,
                        "source": "youtube",
                        "name": data.get("name", influencer_name),
                        "title": data.get("name", influencer_name),
                        "subscribers": data.get("subscribers", "0"),
                        "Youtube_subscribers": data.get("subscribers", "0"),
                        "followers_count": data.get("subscribers", "0"),
                        "total_views": data.get("total_views", "0"),
                        "total_videos": data.get("total_videos", "0"),
                        "link": data.get("link", "")
                    }
                elif platform.lower() == "facebook":
                    result = {
                        "error": False,
                        "username": username,
                        "source": "facebook",
                        "name": data.get("name", influencer_name),
                        "title": data.get("name", influencer_name),
                        "followers_count": data.get("followers_count", "0"),
                        "Facebook_followers": data.get("followers_count", "0"),
                        "url": data.get("url", "")
                    }
                elif platform.lower() == "tiktok":
                    result = {
                        "error": False,
                        "username": username,
                        "source": "tiktok",
                        "name": data.get("name", influencer_name),
                        "followers_count": data.get("followers_count", "0"),
                        "Tiktok_followers": data.get("followers_count", "0"),
                        "link": data.get("link", "")  # Don't generate fake URLs
                    }

                # Check if we got a valid follower count
                if (platform.lower() == "instagram" or platform.lower() == "facebook" or platform.lower() == "tiktok"):
                    if result["followers_count"] == "0" or not result["followers_count"]:
                        # Try to extract from text if JSON parsing failed
                        follower_patterns = [
                            r'(\d+(?:\.\d+)?[KMB]?) followers',
                            r'followers:?\s*(\d+(?:\.\d+)?[KMB]?)',
                            r'follower count:?\s*(\d+(?:\.\d+)?[KMB]?)',
                            r'has (\d+(?:\.\d+)?[KMB]?) followers'
                        ]

                        for pattern in follower_patterns:
                            follower_match = re.search(pattern, response, re.IGNORECASE)
                            if follower_match:
                                result["followers_count"] = follower_match.group(1)
                                if platform.lower() == "instagram":
                                    result["Instagram_followers"] = follower_match.group(1)
                                elif platform.lower() == "facebook":
                                    result["Facebook_followers"] = follower_match.group(1)
                                elif platform.lower() == "tiktok":
                                    result["Tiktok_followers"] = follower_match.group(1)
                                break

                elif platform.lower() == "youtube":
                    if result["subscribers"] == "0" or not result["subscribers"]:
                        # Try to extract from text if JSON parsing failed
                        sub_patterns = [
                            r'(\d+(?:\.\d+)?[KMB]?) subscribers',
                            r'subscribers:?\s*(\d+(?:\.\d+)?[KMB]?)',
                            r'subscriber count:?\s*(\d+(?:\.\d+)?[KMB]?)',
                            r'(\d+(?:\.\d+)?[KMB]?) subs',
                            r'has (\d+(?:\.\d+)?[KMB]?) subscribers'
                        ]

                        for pattern in sub_patterns:
                            sub_match = re.search(pattern, response, re.IGNORECASE)
                            if sub_match:
                                result["subscribers"] = sub_match.group(1)
                                result["Youtube_subscribers"] = sub_match.group(1)
                                result["followers_count"] = sub_match.group(1)
                                break

                # Set error flag based on followers count
                if platform.lower() == "youtube":
                    result["error"] = result["subscribers"] == "0"
                else:
                    result["error"] = result["followers_count"] == "0"

                if not result["error"]:
                    logger.info(f"Successfully fetched missing {platform} data for {influencer_name} using direct prompt")
                    return result

            # If direct prompt failed, fall back to regular methods
            logger.warning(f"Direct prompt failed for {platform} data for {influencer_name}, falling back to regular methods")

        # Call the appropriate platform-specific function as fallback
        if platform.lower() == "instagram":
            result = await get_instagram_details(username)
        elif platform.lower() == "youtube":
            result = await fetch_youtube_feed(username)
        elif platform.lower() == "facebook":
            result = await get_facebook_details(username)
        elif platform.lower() == "tiktok":
            result = await get_tiktok_details(username)
        else:
            logger.error(f"Unknown platform: {platform}")
            return fallback_data

        # If we got a result, ensure it has the name field
        if result and isinstance(result, dict):
            if "name" not in result or not result["name"]:
                result["name"] = influencer_name

            # Ensure followers_count is a string
            if "followers_count" in result and result["followers_count"] is not None:
                if not isinstance(result["followers_count"], str):
                    result["followers_count"] = str(result["followers_count"])
            else:
                result["followers_count"] = "0"

            # Ensure platform-specific follower fields are set
            if platform.lower() == "instagram" and "Instagram_followers" not in result:
                result["Instagram_followers"] = result["followers_count"]
            elif platform.lower() == "youtube" and "Youtube_subscribers" not in result:
                result["Youtube_subscribers"] = result.get("subscribers", "0")
            elif platform.lower() == "facebook" and "Facebook_followers" not in result:
                result["Facebook_followers"] = result["followers_count"]
            elif platform.lower() == "tiktok" and "Tiktok_followers" not in result:
                result["Tiktok_followers"] = result["followers_count"]

            # Set error flag based on followers count
            if platform.lower() == "youtube":
                result["error"] = result.get("subscribers", "0") == "0"
            else:
                result["error"] = result["followers_count"] == "0"

            logger.info(f"Successfully fetched missing {platform} data for {influencer_name}")
            return result
        else:
            logger.error(f"Failed to get {platform} data for {influencer_name}")
            return fallback_data

    except Exception as e:
        logger.error(f"Error fetching missing {platform} data for {influencer_name}: {e}")
        return fallback_data


async def discover_influencers(category: str, location: str = "", count: int = 5) -> list:
    """
    Discover influencers in a specific category and location using Perplexity AI.

    Args:
        category (str): The category of influencers to discover (e.g., "Technology", "Fashion")
        location (str, optional): The location to filter influencers by. Defaults to "" (global).
        count (int, optional): The number of influencers to discover. Defaults to 5.

    Returns:
        list: A list of influencer objects with basic information
    """
    logger.info(f"Discovering {count} influencers in {category} category{' in '+location if location else ''}")

    # Create location text for the prompt
    location_text = f" in {location}" if location else " globally"

    # Create a detailed prompt for Perplexity
    prompt = f"""
    I need the top {count} REAL, VERIFIED influencers in the {category} category{location_text}. These must be actual people/brands with real follower numbers, not fictional examples.

    SEARCH FOR REAL INFLUENCERS AND THEIR SOCIAL MEDIA HANDLES:
    1. Search "top {category} influencers 2024" + "followers" + "Instagram" + "YouTube"
    2. Look for actual influencer ranking websites (Social Blade, HypeAuditor, Influencer Marketing Hub)
    3. Find real people who are famous in {category} with verified social media accounts
    4. Check recent news articles about popular {category} creators
    5. Look for actual brand partnerships and sponsorship announcements in {category}
    6. IMPORTANT: For each influencer, search for their ACTUAL social media handles:
       - Search "[influencer name] Instagram handle"
       - Search "[influencer name] YouTube channel"
       - Search "[influencer name] Facebook page"
       - Search "[influencer name] TikTok account"

    EXAMPLES OF WHAT I WANT:
    - Real people like "MrBeast" (if Technology), "James Charles" (if Beauty), "Gordon Ramsay" (if Food)
    - Actual usernames like "mrbeast", "jamescharles", "gordongram"
    - Real follower counts like "180M", "23.5M", "12.8M"
    - Specific descriptions of their actual content

    UNACCEPTABLE RESPONSES:
    - Generic names like "John Smith" or "Jane Doe"
    - Placeholder usernames like "username123" or "example_user"
    - Vague descriptions like "creates content in {category}"
    - Estimated or approximate follower counts

    VALIDATION REQUIREMENTS:
    - Each influencer must be a real person/brand you can verify exists
    - Follower counts must be actual numbers from real sources
    - Usernames must be real social media handles
    - Names must be real full names of actual influencers

    Return ONLY this JSON format with REAL influencers and their ACTUAL social media handles:
    [
        {{
            "name": "Real Full Name",
            "description": "Specific description of their actual {category} content",
            "primary_platform": "Instagram/YouTube/TikTok/Facebook",
            "estimated_followers": "real_number_with_K_M_B_suffix",
            "verification_status": "verified/unverified",
            "content_type": "specific type of {category} content they create",
            "social_handles": {{
                "instagram": "actual_instagram_handle_without_@",
                "youtube": "actual_youtube_handle_without_@",
                "facebook": "actual_facebook_handle_without_@",
                "tiktok": "actual_tiktok_handle_without_@"
            }}
        }}
    ]

    IMPORTANT: Find the REAL social media handles for each influencer. For example:
    - If the influencer is "Ify Mogekwu", find her actual YouTube handle "@Ifyskitchen"
    - Don't just convert names to usernames - search for their real handles
    - If you can't find a real handle for a platform, use "" (empty string)

    If you cannot find {count} real, verifiable influencers in {category}, return fewer influencers rather than making up fake ones.
    """

    try:
        # Call the Perplexity API with web search enabled (async version to avoid blocking)
        response = await perplexity_data_async(prompt, api_key=PERPLEXITY_API_KEY, options=True)

        # Extract JSON from the response
        data = extract_json_from_text(response)

        if data and isinstance(data, list):
            # Log validation results but accept all influencers from Perplexity
            all_influencers = []
            for influencer in data:
                # Check if this looks like a real influencer (for logging only)
                name = influencer.get("name", "")

                # Check if we have social handles
                social_handles = influencer.get("social_handles", {})

                # Accept all influencers from Perplexity without validation
                has_valid_handles = any(handle for handle in social_handles.values() if handle)
                logger.info(f"Influencer {name} has social handles: {has_valid_handles}")

                # Accept all influencers from Perplexity
                all_influencers.append(influencer)

            # Limit to requested count
            influencers = all_influencers[:count] if count else all_influencers
            logger.info(f"Successfully discovered {len(influencers)} influencers in {category}{location_text} (requested: {count}, total from Perplexity: {len(data)})")

            # Format the influencers into our standard structure
            formatted_influencers = []
            for influencer in influencers:
                # Get the actual social media handles from the response
                social_handles = influencer.get("social_handles", {})

                # Create a basic influencer object without generating usernames
                formatted_influencer = {
                    "name": influencer.get("name", ""),
                    "username": social_handles.get("instagram", "") or social_handles.get("youtube", "") or "",  # Don't generate from name
                    "category": category,
                    "location": location,
                    "description": influencer.get("description", ""),
                    "followers": influencer.get("estimated_followers", "0"),
                    "total_followers": influencer.get("estimated_followers", "0"),
                    "engagement_rate": 0.0,  # Will be calculated later if possible
                    "social_media": {
                        "instagram": {},
                        "youtube": {},
                        "facebook": {},
                        "tiktok": {}
                    },
                    "contact": {},
                    # Store the actual handles for later use
                    "actual_handles": {
                        "instagram": social_handles.get("instagram", ""),
                        "youtube": social_handles.get("youtube", ""),
                        "facebook": social_handles.get("facebook", ""),
                        "tiktok": social_handles.get("tiktok", "")
                    }
                }
                formatted_influencers.append(formatted_influencer)

            return formatted_influencers
        else:
            # If we couldn't parse the JSON or it's not a list, try to extract names directly
            logger.warning(f"Failed to extract JSON array from Perplexity response for {category} influencers")

            # Try to extract names using regex
            name_matches = re.findall(r'"name":\s*"([^"]+)"', response)
            if name_matches:
                logger.info(f"Extracted {len(name_matches)} influencer names using regex")

                # Create basic influencer objects from the extracted names
                formatted_influencers = []
                limited_names = name_matches[:count] if count else name_matches  # Limit to requested count
                for name in limited_names:
                    # Create a basic influencer object without generating fake usernames
                    formatted_influencer = {
                        "name": name,
                        "username": "",  # Don't generate fake usernames
                        "category": category,
                        "location": location,
                        "description": f"Influencer in the {category} category",
                        "followers": "0",
                        "total_followers": "0",
                        "engagement_rate": 0.0,
                        "social_media": {
                            "instagram": {},
                            "youtube": {},
                            "facebook": {},
                            "tiktok": {}
                        },
                        "contact": {}
                    }
                    formatted_influencers.append(formatted_influencer)

                return formatted_influencers

            # If all else fails, return an empty list
            logger.error(f"Could not extract any influencer data for {category}")
            return []

    except Exception as e:
        logger.error(f"Error discovering influencers in {category}: {e}")
        return []
