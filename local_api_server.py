#!/usr/bin/env python3
"""
Local FastAPI Server for Testing Social Media Scrapers

This script creates a local FastAPI server that mimics the production server endpoints
for testing the social media scrapers. It allows you to test the scraping functionality
without affecting the production server.

Usage:
    python local_api_server.py

Then open your browser to:
    http://localhost:8002/docs
"""

import uvicorn
from ds_apis import app
import logging
from loguru import logger
import sys
import os
import signal
import json

# NOTE: All scraper modules are now handled by ds_apis.py

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("local_server.log")
    ]
)

# Check if results directory exists
results_dir = os.path.join(os.path.dirname(__file__), "results")
os.makedirs(results_dir, exist_ok=True)

# Ensure the tasks.json file exists
tasks_file = os.path.join(results_dir, "tasks.json")
if not os.path.exists(tasks_file):
    try:
        with open(tasks_file, 'w') as f:
            f.write('{}')
        logger.info(f"Created empty tasks file at {tasks_file}")
    except Exception as e:
        logger.error(f"Failed to create tasks file: {e}")

# Enable graceful shutdown
def handle_exit(*args, **kwargs):
    logger.info("Shutting down server...")
    logger.info("Server stopped.")
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, handle_exit)
signal.signal(signal.SIGTERM, handle_exit)

if __name__ == "__main__":
    logger.info("Starting Local Influencer API server")

    # Ensure proper Python environment
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Running in directory: {os.path.abspath(os.path.dirname(__file__))}")

    # Check configuration
    logger.info(f"Results directory: {results_dir}")
    logger.info(f"Tasks file: {tasks_file}")
    if os.path.exists(tasks_file):
        try:
            with open(tasks_file, 'r') as f:
                loaded_tasks = json.load(f)
                logger.info(f"Loaded {len(loaded_tasks)} tasks from file")
        except Exception as e:
            logger.error(f"Error loading tasks from file: {e}")

    # Run the server with host and port configuration
    logger.info("Starting server on 0.0.0.0:8002")
    try:
        uvicorn.run(
            "ds_apis:app",
            host="0.0.0.0",
            port=8002,
            log_level="info",
            reload=True,  # Set to True for development
            workers=1  # Using a single worker to ensure task state consistency
        )
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        handle_exit()

# No hardcoded influencer lists - all data comes from Perplexity AI
# NOTE: All endpoints are now handled by ds_apis.py which includes the improved workflow
# This file only starts the server and imports the enhanced functions
