#!/usr/bin/env python3
"""
Simple script to check task status
"""

import requests
import json

# The task ID from the server logs
task_id = "8ccb200e-7c5c-452b-9004-be88cfb2b45e"
base_url = "http://localhost:8002"

print(f"Checking status for task: {task_id}")

# Check task status
try:
    response = requests.get(f"{base_url}/api/check-task-status/{task_id}")
    print(f"Status check response: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Task status: {data.get('status')}")
        print(f"Full response: {json.dumps(data, indent=2)}")
    else:
        print(f"Error: {response.text}")
except Exception as e:
    print(f"Error checking status: {e}")

print("\n" + "="*50)

# Try to get results (even if not completed)
try:
    response = requests.get(f"{base_url}/api/get-task-result/{task_id}")
    print(f"Result check response: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Task has results!")
        if 'result' in data:
            result = data['result']
            if isinstance(result, dict) and 'influencers' in result:
                print(f"Number of influencers: {len(result['influencers'])}")
                for i, inf in enumerate(result['influencers'][:2]):  # Show first 2
                    print(f"Influencer {i+1}: {inf.get('name', 'Unknown')}")
            else:
                print(f"Result structure: {type(result)}")
        else:
            print(f"No result field in response")
    elif response.status_code == 202:
        print("Task not completed yet")
    else:
        print(f"Error getting results: {response.text}")
except Exception as e:
    print(f"Error getting results: {e}")
