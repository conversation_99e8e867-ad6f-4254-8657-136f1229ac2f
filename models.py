from pydantic import BaseModel
from typing import Optional, List, Dict, Any

class InfluencerRequest(BaseModel):
    """Request model for starting a category influencer data task."""
    category: str
    location: str = ""
    count: int = 5

class TaskResponse(BaseModel):
    """Response model for the start-category-influencer-data endpoint."""
    task_id: str

class TaskStatusResponse(BaseModel):
    """Response model for the check-task-status endpoint."""
    task_id: str
    status: str

class TaskResultResponse(BaseModel):
    """Response model for the get-task-result endpoint."""
    task_id: str
    result: Dict[str, Any]
