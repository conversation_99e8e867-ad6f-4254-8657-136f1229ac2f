#!/usr/bin/env python3
"""
Global Rate Limiter Module

This module provides a global rate limiter that can be used by all social media scrapers
to coordinate rate limiting across the application. This helps prevent circular imports
by separating the rate limiter from the social_media_collection module.
"""

import asyncio
import time
import random
from loguru import logger

class GlobalRateLimiter:
    """
    Global rate limiter for all API calls to prevent hitting rate limits.

    This class implements a simple rate limiting mechanism that ensures
    no more than a specified number of calls are made within a time period.
    """
    def __init__(self, calls_per_minute=50):
        self.calls_per_minute = calls_per_minute
        self.call_timestamps = []
        self.lock = asyncio.Lock()

    async def wait(self):
        """
        Wait until a request can be made without exceeding the rate limit.

        This method blocks until it's safe to make another request without
        exceeding the configured rate limit.
        """
        async with self.lock:
            now = time.time()
            # Remove timestamps older than 1 minute
            self.call_timestamps = [ts for ts in self.call_timestamps if now - ts < 60]

            # If we've reached the limit, wait until we can make another call
            if len(self.call_timestamps) >= self.calls_per_minute:
                wait_time = 60 - (now - self.call_timestamps[0]) + random.uniform(0.1, 1.0)
                logger.info(f"Global rate limit reached, waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
                # After waiting, remove timestamps older than 1 minute again
                now = time.time()
                self.call_timestamps = [ts for ts in self.call_timestamps if now - ts < 60]

            # Add current timestamp
            self.call_timestamps.append(now)

# Create a global rate limiter instance (increased for Pro version)
global_rate_limiter = GlobalRateLimiter(calls_per_minute=100)
