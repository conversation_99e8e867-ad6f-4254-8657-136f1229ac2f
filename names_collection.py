from openai import OpenAI
import os
import requests
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from tavily import TavilyClient
from langchain_core.prompts.prompt import PromptTemplate
from langchain_core.output_parsers import Str<PERSON>utputParser, JsonOutputParser
import concurrent.futures
import json
from loguru import logger
from pydantic import BaseModel, <PERSON>
from typing import Optional, List, Dict, Union
import re

load_dotenv()

# os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
llm = ChatOpenAI(model="gpt-4o")

API_KEY = os.getenv('PERPLEXITY_AI_API')

# Define Pydantic models for structured outputs
class InfluencerNamesList(BaseModel):
    names: List[str]

def perplexity_data(prompt_or_payload, system_prompt="Be precise and concise.", options=False, api_key=API_KEY):
    url = "https://api.perplexity.ai/chat/completions"

    # Check if prompt_or_payload is already a complete payload
    if isinstance(prompt_or_payload, dict) and "messages" in prompt_or_payload:
        payload = prompt_or_payload
    else:
        # Build payload from prompt
        payload = {
            # "model": "llama-3.1-sonar-huge-128k-online",
            "model": "sonar-reasoning",
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": prompt_or_payload
                }
            ],
            "temperature": 0.2,
            "top_p": 0.9,
            "return_citations": True,
            #"search_domain_filter": ["perplexity.ai"],
            "return_images": False,
            "return_related_questions": False,
            "search_recency_filter": "month",
            "top_k": 0,
            "stream": False,
            "presence_penalty": 0,
            "frequency_penalty": 1
        }

    if options:
        # join to payload
        payload.update({
            "web_search_options": {
                "search_context_size": "high"
            }
        })
        print(payload)

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # Use synchronous request with SSL verification disabled if needed
    try:
        import ssl
        from urllib3.util import ssl_
        requests.packages.urllib3.disable_warnings()
        response = requests.post(
            url,
            json=payload,
            headers=headers,
            verify=False  # Disable SSL verification
        )
    except (ImportError, AttributeError):
        # Fall back to regular request if urllib3 isn't available or configured properly
        response = requests.post(url, json=payload, headers=headers)

    # Check if the request was successful
    if response.status_code == 200:
        response_data = response.json()
        try:
            # Extract the message content
            message_content = response_data['choices'][0]['message']['content']

            # If it's JSON format and response_format was specified, try to parse it
            if "response_format" in payload and payload["response_format"]["type"] == "json_schema":
                try:
                    return json.loads(message_content)
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse JSON from structured response: {message_content}")
                    return message_content
            else:
                # filter the <think> and <end_think> tags and everything in between
                message_content = re.sub(r'<think>.*?</think>', '', message_content)
                message_content = re.sub(r'<end_think>.*?</end_think>', '', message_content)
                message_content = message_content.replace("\n", "")
            logger.info(f"Perplexity data: {message_content}")
            return message_content
        except (KeyError, IndexError):
            return "Unexpected response format."
    else:
        return f"Request failed with status code: {response.status_code}, message: {response.text}"

def extract_names(influencer_names:str) -> dict:
    logger.info(f"Formatting Influencer Data")
    initiator_prompt = PromptTemplate(
        template="""<|begin_of_text|><|start_header_id|>system<|end_header_id|>
        You are a Influencer Data Extractor AI Agent tasked with extracting information from a search result\n
        You are provided with three informations: \n
            1. Search result of influencers names

        Your job is to extract all the influencers names\n
        Make sure to extract all the names there \n
        You are to return this as a JSON output. The key should be 'names' in the JSON \n
        Do not add to the search result, just return the JSON data of all the influencer names as expected.\n

    <|eot_id|><|start_header_id|>user<|end_header_id|>
    INFLUENCER_NAMEs: {influencer_names}\n

    <|eot_id|><|start_header_id|>assistant<|end_header_id|>""",
            input_variables=["influencer_names"],
        )

    initiator_router = initiator_prompt | llm | JsonOutputParser()
    output = initiator_router.invoke({"influencer_names":influencer_names})
    return output

def get_category_influencers(product_category: str, location="", count=None):
    logger.info("Search for influencers names")

    # Create a more natural query based on whether location is provided
    location_query = ""
    if location:
        location_query = f" in {location}"
    else:
        location_query = " globally"

    # Log what we're searching for
    location_log = f" in {location}" if location else ""
    count_log = f" (requesting {count})" if count else ""
    logger.info(f"Searching for {product_category} influencers{location_log}{count_log}")

    # Create count-specific prompt
    count_instruction = ""
    if count:
        count_instruction = f" Give me exactly {count} influencers."
    else:
        count_instruction = " Give me all the top and popular influencers available."

    # Use Perplexity API to get influencers
    # Create payload with instructions to return JSON format in the prompt itself
    # instead of using the structured outputs parameter
    payload = {
        "model": "sonar-reasoning",
        "messages": [
            {
                "role": "system",
                "content": "You are an influencer research assistant. You must respond with a valid JSON object containing an array of names under the 'names' key. Example: {'names': ['name1', 'name2']}. ONLY return the JSON object, nothing else."
            },
            {
                "role": "user",
                "content": f"Give me a list of the names of the top and popular {product_category} influencers{location_query}. People that are known across the internet.{count_instruction} Return ONLY a valid JSON object with the key 'names' and an array of strings with the names."
            }
        ],
        "temperature": 0.2,
       #"search_domain_filter": ["perplexity.ai"],
        # Structured output format is commented out as it's not available in current tier
        # "response_format": {
        #     "type": "json_schema",
        #     "json_schema": {"schema": influencers_schema}
        # }
    }

    try:
        # Get response as string
        result = perplexity_data(payload)

        # Try to extract JSON from the string response
        if isinstance(result, str):
            # First, clean up the result by removing thinking tags completely
            result = re.sub(r'<think>.*?</think>', '', result, flags=re.DOTALL)

            # Replace any content after the closing brace of the JSON object, which might be thinking comments
            thinking_pattern = r'(\{.*?\}\s*)(.*)$'
            match = re.search(thinking_pattern, result, re.DOTALL)
            if match:
                # Keep only the JSON part and discard anything after it
                result = match.group(1)

            # Try to find JSON object in the cleaned string with "names" field
            json_match = re.search(r'(\{.*?"names".*?\})', result, re.DOTALL)
            if json_match:
                try:
                    # Parse the matched JSON object
                    json_obj = json_match.group(1)
                    # Make sure we have valid JSON with no trailing characters
                    json_obj = re.sub(r'(\})\s*.*$', r'\1', json_obj)
                    parsed_json = json.loads(json_obj)
                    if "names" in parsed_json and isinstance(parsed_json["names"], list):
                        names = parsed_json["names"]
                        logger.info(f"Found {len(names)} influencers for {product_category}{location_log}")
                        return names
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse extracted JSON: {json_match.group(1)}, error: {str(e)}")

            # Try direct parsing if no match was found
            try:
                # First attempt to clean the string by looking for the JSON pattern
                json_pattern = r'\{[^{]*"names"\s*:\s*\[[^\]]*\][^}]*\}'
                json_match = re.search(json_pattern, result, re.DOTALL)
                if json_match:
                    result = json_match.group(0)

                parsed = json.loads(result)
                if isinstance(parsed, dict) and "names" in parsed:
                    logger.info(f"Found {len(parsed['names'])} influencers for {product_category}{location_log}")
                    return parsed["names"]
                elif isinstance(parsed, list):
                    logger.info(f"Found {len(parsed)} influencers for {product_category}{location_log}")
                    return parsed
            except json.JSONDecodeError:
                # Last resort: try to extract names array directly
                logger.warning(f"Could not parse JSON, trying fallback extraction for {product_category}")

                # Look for an array pattern matching ["name1", "name2", ...]
                array_match = re.search(r'\[\s*"([^"]+)"(?:\s*,\s*"([^"]+)")*\s*\]', result, re.DOTALL)
                if array_match:
                    try:
                        array_str = array_match.group(0)
                        names = json.loads(array_str)
                        logger.info(f"Extracted {len(names)} names using regex for {product_category}")
                        return names
                    except json.JSONDecodeError:
                        pass

                # If that fails, try extracting the array directly from the "names" key
                if "names" in result.lower():
                    try:
                        # Try to find something like "names": ["name1", "name2"]
                        names_match = re.search(r'"names"\s*:\s*(\[.+?\])', result, re.DOTALL)
                        if names_match:
                            try:
                                names_array = json.loads(names_match.group(1))
                                if isinstance(names_array, list):
                                    logger.info(f"Extracted {len(names_array)} names from 'names' key for {product_category}")
                                    return names_array
                            except json.JSONDecodeError:
                                # Try one more time with extra cleanup
                                try:
                                    array_str = names_match.group(1)
                                    # Remove any thinking tags or comments that might be inside the array
                                    array_str = re.sub(r'<.*?>', '', array_str)
                                    # Clean up any content after the closing bracket
                                    array_str = re.sub(r'(\])\s*.*$', r'\1', array_str)
                                    names_array = json.loads(array_str)
                                    if isinstance(names_array, list):
                                        logger.info(f"Extracted {len(names_array)} names after cleanup for {product_category}")
                                        return names_array
                                except json.JSONDecodeError:
                                    pass
                    except Exception as e:
                        logger.error(f"Error extracting names array: {str(e)}")

        # If we got here, all attempts failed
        logger.error(f"Failed to extract names for {product_category} from response: {result}")
        return []

    except Exception as exc:
        logger.error(f"{product_category}{location_log} generated an exception: {exc}")
        return []

# This is the function to get influencers for all product categories using multithreading concurrency
def get_all_names(product_categories: list, location=""):
    all_influencers = {}

    # Log location if provided
    location_log = f" in {location}" if location else ""
    logger.info(f"Getting influencers for {len(product_categories)} categories{location_log}")

    # Using ThreadPoolExecutor for multithreading concurrency
    with concurrent.futures.ThreadPoolExecutor() as executor:
        # Create a dictionary where futures map to their category name
        future_to_category = {executor.submit(get_category_influencers, category, location): category for category in product_categories}

        for future in concurrent.futures.as_completed(future_to_category):
            category = future_to_category[future]
            try:
                influencers = future.result()  # Get result of the completed future
                all_influencers[category] = influencers
                logger.info(f"Retrieved {len(influencers)} influencers for {category}{location_log}")
            except Exception as exc:
                logger.error(f"{category}{location_log} generated an exception: {exc}")
                all_influencers[category] = []  # Add empty list for failed categories

    return all_influencers


# test = get_all_names(['Beauty'])
# print(test['Beauty']['names'])