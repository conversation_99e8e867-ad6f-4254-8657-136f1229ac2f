#!/bin/bash

# Make sure Python interpreter is available
if ! command -v python3 &> /dev/null; then
    echo "Python 3 not found. Please install Python 3."
    exit 1
fi

# Check if required packages are installed
REQUIRED_PACKAGES="fastapi uvicorn loguru aiohttp requests"
for package in $REQUIRED_PACKAGES; do
    if ! python3 -c "import $package" 2> /dev/null; then
        echo "Required package $package not installed."
        read -p "Would you like to install it? (y/n) " answer
        if [ "$answer" = "y" ]; then
            python3 -m pip install $package
        else
            echo "Cannot continue without $package."
            exit 1
        fi
    fi
done

# Create results directory if it doesn't exist
if [ ! -d "results" ]; then
    mkdir -p results
    echo "Created results directory."
fi

# Make scripts executable
chmod +x run_server.py
chmod +x test_api_request.py

# Create or check tasks.json
if [ ! -f "results/tasks.json" ]; then
    echo "{}" > results/tasks.json
    echo "Created empty tasks.json file."
else
    # Validate the JSON file
    if ! python3 -c "import json; json.load(open('results/tasks.json'));" 2> /dev/null; then
        echo "Warning: tasks.json is invalid. Backing up and creating new file."
        mv results/tasks.json results/tasks.json.bak
        echo "{}" > results/tasks.json
        echo "Created new tasks.json file. Old file backed up as tasks.json.bak."
    fi
fi

# Set correct permissions
chmod 644 results/tasks.json

# Clear any stale pid files
if [ -f "server.pid" ]; then
    rm server.pid
fi

echo "Starting Influencer API server..."
python3 run_server.py 